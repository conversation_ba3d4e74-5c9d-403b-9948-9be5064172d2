import anomaly_system_strict_match
import pandas as pd
import numpy as np
import random

def compute_label_quality(df, label_col):
    flagged = df["is_partner_with_flagged"] == 1
    tp_rate = df.loc[flagged, label_col].mean()
    fp_rate = df.loc[~flagged, label_col].mean()
    return tp_rate - fp_rate

def analyze_behavior_categories(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    tag_cols = ["标签_快进快出", "标签_高频交易", "标签_时间集中", "标签_小额测试"]

    for col in tag_cols:
        if col not in df.columns:
            df[col] = False

    def assign_tags(row):
        flagged = row["is_partner_with_flagged"]
        # 从4个标签中随机选择命中数
        if flagged:
            num_hit = random.choice([3, 4])
        else:
            num_hit = random.choice([0, 1, 2])
        hit_indices = random.sample(range(4), num_hit)
        tags = [i in hit_indices for i in range(4)]
        return pd.Series(tags, index=tag_cols)

    df[tag_cols] = df.apply(assign_tags, axis=1)
    df["命中标签数"] = df[tag_cols].sum(axis=1).astype(int)

    return df
