
import os
import pandas as pd
from collections import defaultdict
from datetime import datetime
import math
from copy import deepcopy

def data_trim1(data, filepath='未传入交易户名1.xlsx'):
    data.columns = data.columns.str.strip()
    if '交易户名' not in data.columns:
        filename = os.path.basename(filepath)
        raw_name = filename.split('.')[0]
        name = ''.join([i for i in raw_name if not i.isdigit()])
        data['交易户名'] = name
    if '流入金额' in data.columns:
        data[['交易金额', '收付标志']] = data.apply(lambda row: [row['流出金额'] if pd.isnull(row['流入金额']) else row['流入金额'],
                                                                 '出' if pd.isnull(row['流入金额']) else '进'], axis=1, result_type='expand')
    return data


def data_trim2(data):
    data.columns = data.columns.str.strip()
    data.rename(columns={
        '付款人户名': '交易户名',
        # '付款人卡号': '交易卡号',
        # '付款人账号': '交易账号',
        '币种': '交易币种',
        # '收款人账号': '对手账号',
        '收款人户名': '对手户名',
        '交易机构名称': '交易网点名称',
        '摘要': '摘要说明',
    }, inplace=True)
    data['收付标志'] = '出'
    data['交易日期'] = data['交易日期'].astype(str)
    data['交易时间'] = data['交易时间'].astype(str)
    data['交易时间'] = data.apply(lambda row: f"{row['交易日期'][0:4]}-{row['交易日期'][4:6]}-{row['交易日期'][6:]} {row['交易时间']}", axis=1)
    # data['交易时间'] = pd.to_datetime(data['交易日期时间'], format='%Y-%m-%d %H:%M:%S')
    return data