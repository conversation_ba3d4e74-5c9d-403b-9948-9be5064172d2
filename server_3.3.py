import os
import sys
from sanic.exceptions import BadRequest  # 新增导入
# 将当前目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from sanic import Sanic, response
from sanic.response import json, text
import pymysql
import pandas as pd
from collections import defaultdict
from datetime import datetime
import math
from copy import deepcopy

# 确保能找到anomaly_system_strict_match包
try:
    # 尝试导入包
    import anomaly_system_strict_match
    print("成功导入anomaly_system_strict_match包")
except ImportError as e:
    print(f"警告: 无法导入anomaly_system_strict_match包: {str(e)}")
    print("将使用测试数据代替")


# 创建 Sanic 应用实例
app = Sanic("SimpleServer")

def data_trim1(data, filepath='未传入交易户名1.xlsx'):
    data.columns = data.columns.str.strip()
    if '交易户名' not in data.columns:
        filename = os.path.basename(filepath)
        raw_name = filename.split('.')[0]
        name = ''.join([i for i in raw_name if not i.isdigit()])
        data['交易户名'] = name
    if '流入金额' in data.columns:
        data[['交易金额', '收付标志']] = data.apply(lambda row: [row['流出金额'] if pd.isnull(row['流入金额']) else row['流入金额'],
                                                                 '出' if pd.isnull(row['流入金额']) else '进'], axis=1, result_type='expand')
    return data


def data_trim2(data):
    data.columns = data.columns.str.strip()
    data.rename(columns={
        '付款人户名': '交易户名',
        # '付款人卡号': '交易卡号',
        # '付款人账号': '交易账号',
        '币种': '交易币种',
        # '收款人账号': '对手账号',
        '收款人户名': '对手户名',
        '交易机构名称': '交易网点名称',
        '摘要': '摘要说明',
    }, inplace=True)
    data['收付标志'] = '出'
    data['交易日期'] = data['交易日期'].astype(str)
    data['交易时间'] = data['交易时间'].astype(str)
    data['交易时间'] = data.apply(lambda row: f"{row['交易日期'][0:4]}-{row['交易日期'][4:6]}-{row['交易日期'][6:]} {row['交易时间']}", axis=1)
    # data['交易时间'] = pd.to_datetime(data['交易日期时间'], format='%Y-%m-%d %H:%M:%S')
    return data


# def load_to_mysql(json_data):
#     # 这个函数可能没用了，因为2.0版更新成了load_to_mysql_with_duplicate_checking
#     suc_line = 0
#     fail_line = 0
#     data = pd.DataFrame(json_data)
#     if '交易日期' not in data.columns:
#         data = data_trim1(data)
#     else:
#         data = data_trim2(data)
#     data = data[['交易户名', '交易卡号', '交易账号', '交易时间', '收付标志', '交易金额', '交易余额', '交易币种', '对手账号', '对手户名', '交易网点名称', '对手开户银行', '备注', '摘要说明', 'IP地址', 'MAC地址']]
#     data = data.astype(str)
#     data = data.applymap(lambda x: None if pd.isna(x) or x == 'nan' else x)
#     connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
#     cursor = connection.cursor()
#
#     create_table_query = f"""
#         CREATE TABLE IF NOT EXISTS data_to_mysql (
#             `交易户名` VARCHAR(64) NOT NULL,
#             `交易卡号` VARCHAR(64) NOT NULL,
#             `交易账号` varchar(64),
#             `交易时间` DATETIME,
#             `收付标志` VARCHAR(64),
#             `交易金额` DECIMAL(18, 2),
#             `交易余额` DECIMAL(18,2),
#             `交易币种` varchar(64),
#             `对手账号` VARCHAR(64),
#             `对手户名` VARCHAR(64),
#             `交易网点名称` VARCHAR(64),
#             `对手开户银行` varchar(64),
#             `备注` varchar(512),
#             `摘要说明` varchar(512),
#             `IP地址` varchar(512),
#             `MAC地址` varchar(512)
#         );
#         """
#
#     insert_query = f"""
#         INSERT INTO data_to_mysql (
#             交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种,
#             对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, IP地址, MAC地址
#         )
#         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
#         """
#
#     cursor.execute(create_table_query)
#     for _, row in data.iterrows():
#
#         if row['交易户名'] is None or row['交易卡号'] is None or row['交易时间'].strip() is None or row['交易金额'] == '':
#             fail_line += 1
#             continue
#         suc_line += 1
#
#         cursor.execute(insert_query, (
#             row['交易户名'],
#             row['交易卡号'],
#             row['交易账号'],  # 新增字段
#             row['交易时间'],
#             row['收付标志'],
#             row['交易金额'],
#             row['交易余额'],  # 新增字段
#             row['交易币种'],  # 新增字段
#             row['对手账号'],
#             row['对手户名'],
#             row['交易网点名称'],
#             row['对手开户银行'],  # 新增字段
#             row['备注'],
#             row['摘要说明'],  # 新增字段
#             row['IP地址'],  # 后加的字段
#             row['MAC地址']  # 后加的字段
#         ))
#
#     connection.commit()
#
#     # 关闭游标和连接
#     cursor.close()
#     connection.close()
#     result = {
#         "success_lines": suc_line,
#         "fail_lines": fail_line
#     }
#
#     return result


def load_to_mysql_with_duplicate_checking(target_form, json_data, username, filenames, tablename):
    """
    把json_data中的数据存到target_form中
    :param target_form: 要插入的数据表的名称
    :param json_data: 存储要插入数据的json文件
    :return: None
    """
    # 记录成功函数和失败行数
    suc_line = 0
    fail_line = 0

    # 整理json数据成格式合适的dataframe数据
    data = pd.DataFrame(json_data)
    if '交易日期' not in data.columns:
        data = data_trim1(data)
    else:
        data = data_trim2(data)
    data = data[['交易户名', '交易卡号', '交易账号', '交易时间', '收付标志', '交易金额', '交易余额', '交易币种', '对手账号', '对手户名', '交易网点名称', '对手开户银行', '备注', '摘要说明', 'IP地址', 'MAC地址']]
    data = data.astype(str)
    data = data.applymap(lambda x: None if pd.isna(x) or x == 'nan' else x)

    # 如果要插入的数据表不存在则创建    //***********
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()

    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {target_form} (
            `交易户名` VARCHAR(64) NOT NULL,
            `交易卡号` VARCHAR(64) NOT NULL,
            `交易账号` varchar(64),
            `交易时间` DATETIME,
            `收付标志` VARCHAR(64),
            `交易金额` DECIMAL(18, 2),
            `交易余额` DECIMAL(18,2),
            `交易币种` varchar(64),
            `对手账号` VARCHAR(64),
            `对手户名` VARCHAR(64),
            `交易网点名称` VARCHAR(64),
            `对手开户银行` varchar(64),
            `备注` varchar(512),
            `摘要说明` varchar(512),
            `IP地址` varchar(512),
            `MAC地址` varchar(512)
        );
        """
    cursor.execute(create_table_query)

    # ✅ 插入：检查并添加缺失字段，最开始数据表都没有IP地址和MAC地址字段，这段代码能补上
    required_columns = ['IP地址', 'MAC地址']
    cursor.execute(f"SHOW COLUMNS FROM {target_form}")
    existing_columns = set([col[0] for col in cursor.fetchall()])
    print()
    print(existing_columns)
    for col in required_columns:
        if col not in existing_columns:
            cursor.execute(f"ALTER TABLE {target_form} ADD COLUMN `{col}` VARCHAR(512)")

    # 查原表中的所有人名，2.0新增
    cursor.execute(f"SELECT DISTINCT 交易户名, 对手户名 FROM {target_form}")
    org_members = set()
    for row in cursor.fetchall():
        org_members.update(row)  # 直接将两个元素分别加入 set

    # 查临时表中所有人名，2.0新增
    new_members = set(data["交易户名"].unique()) | set(data["对手户名"].unique())

    # 取交集，检测新数据中那些户名和原数据中的户名重复了，2.0新增
    intersection_list = list(org_members & new_members)
    if '' in intersection_list:
        intersection_list.remove('')

    # 把新数据插入到数据表中，并更新成功行数和失败行数
    insert_query = f"""
        INSERT INTO {target_form} (
            交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种,
            对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, `IP地址`, `MAC地址`
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

    for _, row in data.iterrows():

        if row['交易户名'] is None or row['交易卡号'] is None or row['交易时间'].strip() is None or row['交易金额'] == '':
            fail_line += 1
            continue
        suc_line += 1

        cursor.execute(insert_query, (
            row['交易户名'],
            row['交易卡号'],
            row['交易账号'],  # 新增字段
            row['交易时间'],
            row['收付标志'],
            row['交易金额'],
            row['交易余额'],  # 新增字段
            row['交易币种'],  # 新增字段
            row['对手账号'],
            row['对手户名'],
            row['交易网点名称'],
            row['对手开户银行'],  # 新增字段
            row['备注'],
            row['摘要说明'],  # 新增字段
            row['IP地址'],  # 后加的字段
            row['MAC地址']  # 后加的字段
        ))

    connection.commit()

    insert_query = f"""
        INSERT INTO log (user, filename,tablename) VALUES (%s,%s,%s);
        """
    for filename in filenames:
        cursor.execute(insert_query,(username, filename, tablename))
    connection.commit()

    # 关闭游标和连接, 2.0在result中加了intersection
    cursor.close()
    connection.close()
    result = {
        "success_lines": suc_line,
        "fail_lines": fail_line,
        "intersection": intersection_list
    }

    return result


def get_forms():
    """
    查找数据库中所有表名
    :return: 所用表名构成的列表tables
    """
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    cursor.execute("SHOW TABLES")
    tables = [table[0] for table in cursor.fetchall()]
    if 'log' in tables:
        tables.remove('log')
    return tables


def is_number(s):
    try:
        float(s)  # 尝试转换为浮点数
        return True
    except ValueError:
        return False


# @app.middleware("response")
# async def add_cors_headers(request, response):
#     origin = request.headers.get("Origin")
#     if origin:
#         response.headers["Access-Control-Allow-Origin"] = origin
#     response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
#     response.headers["Access-Control-Allow-Headers"] = "Content-Type"
@app.middleware("response")
async def add_cors_headers(request, response):
    # 获取请求的 Origin
    origin = request.headers.get("Origin")
    if origin:
        # 动态设置允许的 Origin
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Vary"] = "Origin"  # 告诉浏览器不同 Origin 会返回不同结果
    else:
        response.headers["Access-Control-Allow-Origin"] = "*"  # 如果没有 Origin，允许所有

    # 添加其他 CORS 必要头信息
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    response.headers["Access-Control-Allow-Credentials"] = "true"  # 如果需要支持 cookies

    # 处理 OPTIONS 请求
    if request.method == "OPTIONS":
        response.status = 204  # OPTIONS 请求需要返回 204 无内容状态码
        response.body = b""  # 不需要返回任何内容


# 中间件捕获所有请求
@app.middleware("request")
async def log_request(request):
    # 输出请求信息
    print(f"Request received: {request.method} {request.path}")
    print(f"Headers: {request.headers}")
    print(f"Body: {request.body.decode('utf-8', errors='ignore') if request.body else 'No body'}")


@app.route("/hello", methods=["GET"])
async def hello(request):
    data = {"message": "Hello, this is a Sanic server.", "status": "success"}
    return json(data)


@app.route("/transaction_history", methods=["POST"])
async def transaction_history(request):
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    data = request.json
    name = data.get("username")
    startDate = data.get("startDate")
    endDate = data.get("endDate")
    timeScale = data.get("timeScale")
    selectedAccounts = data.get("selectedAccounts", None)
    table_name = data.get("tableName") or "测试数据"
    records = None
    select_query = f"""
        select distinct 交易卡号 from {table_name} where 交易户名 = %s;
    """
    cursor.execute(select_query,(name))
    accounts = cursor.fetchall()
    accounts = [account[0] for account in accounts]

    select_query = f"""
        select distinct 对手账号 from {table_name} where 对手户名 = %s;
    """
    cursor.execute(select_query,(name))
    accounts_opponent = cursor.fetchall()
    accounts_opponent = [account[0] for account in accounts_opponent]
    accounts = accounts + accounts_opponent

    if len(selectedAccounts) == 0:
        selectedAccounts = accounts
    for account in selectedAccounts:
        select_query = f"""
            select 交易时间,收付标志,交易金额,交易户名,对手户名 from {table_name} where (交易卡号 = %s or 对手账号 = %s) and 交易时间 between %s and %s;
        """
        cursor.execute(select_query,(account,account,startDate, endDate))
        results = cursor.fetchall()
        if records is None:
            records = [list(result) for result in results]
        else:
            records.extend(list(result) for result in results)
    print(selectedAccounts)
    print(records)
    deal = defaultdict(lambda: [0,0,0])
    # 按天统计
    cursor.close()
    connection.close()
    for dt, status, money,username,username_opponent in records:
        year = dt.year
        money = abs(float(money))
        if timeScale == 'year':
            if status == '进':
                if username == name:
                    deal[year][0] += money
                    deal[year][1] += money
                if username_opponent == name:
                    deal[year][0] += money
                    deal[year][2] += money
            else:
                if username == name:
                    deal[year][0] += money
                    deal[year][2] += money
                if username_opponent == name:
                    deal[year][0] += money
                    deal[year][1] += money
        elif timeScale == 'month':
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][1] += money
        elif timeScale == 'day':
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][1] += money
        else:
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][1] += money
    # 使用列表推导式
    # 使用 for 循环遍历字典，插入键到对应的值中
    for key, value in deal.items():
        value.insert(0, key)
    deal = [value for key, value in deal.items()]
    return_dict = {"deal": deal, "accounts": accounts}
    print(return_dict)
    return json(return_dict)


@app.route("/all_transaction_history", methods=["POST"])
async def all_transaction_history(request):  # 查数据表用
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    data = request.json
    account = data.get("account", None)
    name = data.get("name", None)
    page = data.get("page", None)
    limit = data.get("limit", None)
    keywords = data.get('remark', None)
    table_name = data.get("tableName") or "测试数据"
    opname = data.get("opponent", None)
    select_condition = ""
    if name is not None:
        select_condition = select_condition + "交易户名 = '" + name + "'"
    if opname is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "对手户名 = '" + opname + "'"
    if account is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "交易卡号 = " + str(account)
    if keywords is not None:
        if select_condition != "":
            select_condition += " and "
        keys = list(keywords)
        keywords = '%' + '%'.join(keys) + '%'
        select_condition = select_condition + "(备注 LIKE '" + keywords + "' or 摘要说明 LIKE '" + keywords + "')"
    if select_condition != "":
        select_condition += ";"
        query = f"""
            SELECT *
            FROM {table_name}
            WHERE {select_condition}
            """
    else:
        query = f"""
            SELECT *
            FROM {table_name}
            """
    cursor.execute(query)
    results = cursor.fetchall()
    # print(results)
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: (x[5] is None, x[5] or 0), reverse=True)
    for result in results_list:
        result[3] = result[3].isoformat()
        result[5] = str(result[5])
        result[6] = str(result[6])
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    return json(return_dict)


@app.route("/find_same", methods=["POST"])
async def find_some(request):  # 这个函数后来没用到吧
    data = request.json
    persons1_list = data.get('obj1')
    persons2_list = data.get('obj2')
    num = data.get('num')
    table_name = data.get("tableName") or "测试数据"

    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()

    obj1_placeholder = ", ".join(["%s"] * len(persons1_list))
    obj2_placeholder = ", ".join(["%s"] * len(persons2_list))
    query_relation = f'''select 对手户名,count(*), sum(金额1) as 金额
                        from(
                            select 交易户名, 对手户名, count(*),sum(交易金额) as 金额1
                            from {table_name}
                            where 交易户名 in ({obj1_placeholder})
                            group by 交易户名, 对手户名) as s
                        where 交易户名 in ({obj2_placeholder}) and 交易户名 is not null
                        group by 对手户名
                        order by 金额 desc
                        limit {num} '''
    cursor.execute(query_relation, persons1_list + persons2_list)
    sameRivalName = cursor.fetchall()
    cursor.close()
    connection.close()

    sameRivalName = [list(relation) for relation in sameRivalName]
    for relation in sameRivalName:
        relation[2] = str(relation[2])
    return (json(sameRivalName))


@app.route("/find_relation", methods=["POST"])
async def find_relation(request):
    data = request.json
    name = data.get('username')
    num = data.get('minAmount')
    time = data.get('dateRange')
    table_name = data.get("tableName") or "测试数据"
    if num is None:
        num = 1000000
    else:
        try:
            num = float(num)
        except:
            num = 1000000
    time_condition = ""
    if time and len(time) == 2:
       start_time = time[0]  # "2020-01-01 00:00:00"
       end_time = time[1]    # "2020-01-03 00:00:00"
       time_condition = f" AND 交易时间 >= '{start_time}' AND 交易时间 <= '{end_time}'"
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    query = f"""
    WITH 交易关系 AS  (
        SELECT 交易户名, 对手户名, SUM(交易金额) AS 关系金额
        FROM {table_name}
        WHERE 交易户名 IS NOT NULL
        AND 对手户名 IS NOT NULL
        AND 交易金额 IS NOT NULL
        AND 对手户名 != 'None'
        {time_condition}
        GROUP BY 交易户名, 对手户名
        HAVING 关系金额 > %s
        )
        SELECT *
        FROM 交易关系
        WHERE 对手户名 IN (
            SELECT 对手户名
            FROM (
                SELECT 对手户名, COUNT(*) AS 交易次数
                FROM 交易关系
                WHERE 关系金额 > %s
                GROUP BY 对手户名
                HAVING 交易次数 > 1
            ) AS 嫌疑户名
--
        )
        ORDER BY 关系金额
        LIMIT 1000
        """

    cursor.execute(query, (num,num))

    results = cursor.fetchall()
    total_lines = defaultdict(list)
    total_nodes = defaultdict(dict)
    id = 0
    for result in results:
        # 分配id
        if result[0] not in total_nodes.keys():
            total_node = {'id': id, 'color': "rgba(178, 34, 34, 1)", "width": 0, "height": 0}
            total_nodes[result[0]] = total_node
            id += 1
        if result[1] not in total_nodes.keys():
            total_node = {'id': id, 'color': "rgba(178, 34, 34, 1)", "width": 0, "height": 0}
            total_nodes[result[1]] = total_node
            id += 1
        total_line = {'to': result[1], 'text': result[2]}
        total_lines[result[0]].append(total_line)

    # 分配权重 删去无用信息
    lines = []
    nodes = defaultdict(dict)
    usefull_names = []
    if name is None:
        usefull_names = total_nodes.keys()
    else:
        usefull_names = [name]
    id = 0
    level = 0
    while len(usefull_names) != 0:
        mid_usefull_name = []
        for usefull_name in usefull_names:
            if usefull_name not in nodes.keys():
                node = deepcopy(total_nodes[usefull_name])
                node['id'] = id
                node['type'] = 'direct' if level == 0 else 'indirect'  # 👈 标记类型
                nodes[usefull_name] = node
                id += 1
            usefull_lines = total_lines[usefull_name]
            total_money = 0
            for usefull_line in usefull_lines:
                to_name = usefull_line['to']
                if to_name not in nodes.keys():
                    node = deepcopy(total_nodes[to_name])
                    node['id'] = id
                    node['type'] = 'indirect'  # 👈 间接交易
                    nodes[to_name] = node
                    id += 1
                    mid_usefull_name.append(to_name)
                line = {
                    'from': f"{nodes[usefull_name]['id']}",
                    'to': f"{nodes[to_name]['id']}",
                    'text': f"{float(usefull_line['text'])}"
                }
                lines.append(line)
                total_money += int(usefull_line['text'])
            nodes[usefull_name]['width'] = math.log10(total_money + 10) * 10
            nodes[usefull_name]['height'] = math.log10(total_money + 10) * 10
        usefull_names = mid_usefull_name
        level += 1
    nodes_list = []
    for key, value in nodes.items():
        value['text'] = key
        value['id'] = str(value['id'])
        nodes_list.append(value)
    return json({"state": 200, "data": {"nodes": nodes_list, "lines": lines}})


@app.route("/all_relation", methods=["GET"])
async def all_relation(request):
    data = request.json
    table_name = data.get("tableName") or "测试数据"
    num = 1000000
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    query = f"""
    WITH 交易关系 AS  (
        SELECT DISTINCT 交易户名, 对手户名, SUM(交易金额) AS 关系金额
        FROM {table_name}
        WHERE 交易户名 IS NOT NULL
        AND 对手户名 IS NOT NULL
        AND 交易金额 IS NOT NULL
        AND 对手户名 != 'None'
        GROUP BY 交易户名, 对手户名
        )
        SELECT *
        FROM 交易关系
        WHERE 对手户名 IN (
            SELECT 对手户名
            FROM (
                SELECT 对手户名, COUNT(*) AS 交易次数
                FROM 交易关系
                WHERE 关系金额 > %s
                GROUP BY 对手户名
            ) AS 嫌疑户名
            WHERE 交易次数 > 1
        )
        """

    cursor.execute(query, num)

    results = cursor.fetchall()
    total_lines = defaultdict(list)
    total_nodes = defaultdict(dict)
    id = 0
    for result in results:
        # 分配id
        if result[0] not in total_nodes.keys():
            total_node = {'id':id,'color':"rgba(178, 34, 34, 1)","width":0, "height": 0}
            total_nodes[result[0]] = total_node
            id += 1
        if result[1] not in total_nodes.keys():
            total_node = {'id':id,'color':"rgba(178, 34, 34, 1)","width":0, "height": 0}
            total_nodes[result[1]] = total_node
            id += 1
        total_line = {'to':result[1],'text':result[2]}
        total_lines[result[0]].append(total_line)

    # 分配权重 删去无用信息
    lines = []
    nodes = defaultdict(dict)
    usefull_names = total_nodes.keys()
    id = 0
    while len(usefull_names) != 0:
        mid_usefull_name = []
        for usefull_name in usefull_names:
            if usefull_name not in nodes.keys():
                node = deepcopy(total_nodes[usefull_name])
                node['id'] = id
                nodes[usefull_name] = node
                id += 1
            usefull_lines = total_lines[usefull_name]
            total_money = 0
            for usefull_line in usefull_lines:
                to_name = usefull_line['to']
                if to_name not in nodes.keys():
                    node = deepcopy(total_nodes[to_name])
                    node['id'] = id
                    nodes[to_name] = node
                    id += 1
                    mid_usefull_name.append(to_name)
                line = {'from':f"{nodes[usefull_name]['id']}",'to':f"{nodes[to_name]['id']}",'text':f"{float(usefull_line['text'])}"}
                lines.append(line)
                total_money += int(usefull_line['text'])
            nodes[usefull_name]['width'] = math.log10(total_money+10)
            nodes[usefull_name]['height'] = math.log10(total_money+10)
        usefull_names = mid_usefull_name
    nodes_list = []
    for key,value in nodes.items():
        value['text'] = key
        value['id'] = str(value['id'])
        nodes_list.append(value)
    return json({"state":200,"data":{"nodes":nodes_list,"lines":lines}})

@app.route("/search_remark", methods=["POST"])
async def search_remark(request):

    data = request.json
    keywords = data.get('keywords')
    page = data.get("page", None)
    limit = data.get("limit", None)
    table_name = data.get("tableName") or "测试数据"

    keys = list(keywords)
    keywords = '%' + '%'.join(keys) + '%'

    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    query = f"""
        SELECT *
        FROM {table_name}
        WHERE 备注 LIKE %s or 摘要说明 LIKE %s
        """

    cursor.execute(query, (keywords,keywords))

    results = cursor.fetchall()
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: x[5], reverse=True)
    for result in results_list:
        result[3] = result[3].isoformat()
        result[5] = str(result[5])
        result[6] = str(result[6])
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    return json(return_dict)

@app.route('/upload', methods=['POST', 'GET'])
async def upload_data(request):
    # 获取JSON数据
    data = request.json
    target_form = data.get("tableName", "data_to_mysql")
    json_data = data.get("newMember", None)
    username =data.get("user")
    filenames = data.get('filename')
    if json_data:
        # print(json_data)
        result = load_to_mysql_with_duplicate_checking(target_form, json_data,username,filenames,target_form)
        return json(result)
    else:
        return json({'error': 'No JSON data provided'}, status=400)


@app.route('/all_tables', methods=['POST', 'GET'])
async def get_tables(request):
    return json({"all_tables": get_forms()})


def logical_filter(org_form, logic: list, new_form):
    """
    运用正则关系，缩小查询范围，并把结果存到一个表中！！！这里最好要实现且逻辑的优先级是要高于或逻辑的！！！
    不过直接把逻辑筛选传到sql的where里，就可以自动实现且逻辑的优先级大于或逻辑
    :param org_form: 原始的数据表名
    :param logic: 存储各种筛选逻辑，包含attribute（属性），lgc（大于、小于、非这种逻辑关系），content（输入的内容），connect（且或这种连接关系）。
    :param new_form: 新创建的数据表名
    :return: 0（正常），1（没有逻辑筛选，请使用原表），2（输入的属性，逻辑关系，输入内容或连接关系不匹配），3（新表已存在，请删除原新表名后重试），
    4（原表名不存在）
    """
    if len(logic) == 0:
        return 1
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    select_condition_str = ""
    if new_form in get_forms():
        cursor.close()
        return 3
    if org_form not in get_forms():
        cursor.close()
        return 4
    for i in range(len(logic)):
        if i != 0:
            if logic[i]['connect'] == "且":
                select_condition_str += " and "
            elif logic[i]['connect'] == "或":
                select_condition_str += " or "
        select_condition_str += logic[i]['attribute']
        if logic[i]['logical'] == "大于":
            select_condition_str += " > "
        elif logic[i]['logical'] == "大于等于":
            select_condition_str += " >= "
        elif logic[i]['logical'] == "小于":
            select_condition_str += " < "
        elif logic[i]['logical'] == "小于等于": # 这之前传的版本写错了
            select_condition_str += " <= "
        elif logic[i]['logical'] == "等于":
            select_condition_str += " = "
        elif logic[i]['logical'] == "不等于":
            select_condition_str += " != "
        elif logic[i]['logical'] == "是":
            select_condition_str += " is "
        elif logic[i]['logical'] == "非":
            select_condition_str += " is not "
        if logic[i]['content'] == '空' or logic[i]['content'] == 'NULL' or logic[i]['content'] == 'null':
            select_condition_str += "NULL"
        elif is_number(logic[i]['content']):
            select_condition_str += logic[i]['content']
        else:
            select_condition_str = select_condition_str + "'" + logic[i]['content'] + "'"

    select_query = f"""
        select *
        from {org_form}
        where {select_condition_str}
    """

    cursor.execute(select_query)
    query_results = cursor.fetchall()

    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {new_form} (
            `交易户名` VARCHAR(64) NOT NULL,
            `交易卡号` VARCHAR(64) NOT NULL,
            `交易账号` varchar(64),
            `交易时间` DATETIME,
            `收付标志` VARCHAR(64),
            `交易金额` DECIMAL(18, 2),
            `交易余额` DECIMAL(18,2),
            `交易币种` varchar(64),
            `对手账号` VARCHAR(64),
            `对手户名` VARCHAR(64),
            `交易网点名称` VARCHAR(64),
            `对手开户银行` varchar(64),
            `备注` varchar(512),
            `摘要说明` varchar(512),
            `IP地址` varchar(512),
            `MAC地址` varchar(512)
        );
        """
    cursor.execute(create_table_query)
    connection.commit()

    insert_query = f"""
        INSERT INTO {new_form} (
            交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种,
            对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, `IP地址`, `MAC地址`
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
    cursor.executemany(insert_query, query_results)
    connection.commit()
    cursor.close()
    return 0


@app.route('/logical_filter', methods=['POST', 'GET'])
# @app.route('/upload_tables', methods=['POST', 'GET'])
async def logical_filtering(request):
    data = request.json
    org_form = data.get("org_form", "测试数据")
    logic = data.get("logical", None)
    new_form = data.get("new_form", "default_filtered_table")
    return json({"result": logical_filter(org_form, logic, new_form)})

def drop_table(table_name):
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    drop_query = f"DROP TABLE IF EXISTS {table_name};"
    cursor.execute(drop_query)
    connection.commit()
    cursor.close()


@app.route('/all_log_history', methods=['POST', 'GET'])
async def log_search(request):
    data = request.json
    connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
    cursor = connection.cursor()
    page = data.get("page", None)
    limit = data.get("pagelimit", None)
    time = data.get("time", None)
    user = data.get("username", None)
    filename = data.get("fileName", None)
    table = data.get("databaseName", None)
    select_condition = ""
    if time:
        select_condition = "time >= '" + str(time[0]) + "' AND time <= '" + str(time[1]) + "'"
    if user is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "user = '" + user + "'"
    if filename is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "filename = '" + filename + "'"
    if table != "" and table is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "tablename = '" + table + "'"
    if select_condition != "":
        select_condition = "WHERE " + select_condition + ";"
    query = f"""
        SELECT *
        FROM log {select_condition}
        """
    # print(query)
    cursor.execute(query)
    results = cursor.fetchall()
    # print(results)
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: (x[3] is None, x[3]), reverse=True)
    for result in results_list:
        result[0] = result[0].isoformat()
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    print(return_dict)
    return json(return_dict)


@app.route('/drop_table', methods=['POST', 'GET'])
async def drop_a_table(request):
    try:
        data = request.json
        table4drop = data.get("table4drop", None)

        if not table4drop:
            return response.json({"error": "table4drop is required"}, status=400)

        drop_table(table4drop)  # 这里调用你的删除表函数

        return response.json({"isSuccess": 1}, status=200)

    except Exception as e:
        return response.json({"error": str(e)}, status=500)




@app.route('/tianye_demo',methods=['POST','GET']) 
async def transmit(request):
    try:
        print("开始赋分")
        # 确保文件路径是绝对路径
        file_path = os.path.join(os.getcwd(), "data", "田野案资金数据.xlsx")
        print(f"尝试加载文件: {file_path}")

        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return json({"error": f"文件不存在: {file_path}"}, status=404)

        # 运行分析管道
        print("开始运行分析管道...")
        from anomaly_system_strict_match.run import run_pipeline
        df_result = run_pipeline(file_path)
        print(f"分析完成，结果列名: {list(df_result.columns)}")

        # 重命名列以匹配前端期望的格式
        df_renamed = df_result.reset_index().rename(columns={
            'index': 'name',  # 索引作为名称
            'fused_score': 'fused_score',  # 嫌疑分值
            '命中标签数': 'count',  # 命中标签数
            '标签_快进快出': 'flag2',  # 快进快出
            '标签_高频交易': 'flag3',  # 高频交易
            '标签_时间集中': 'flag4',  # 时间集中
            '标签_小额测试': 'flag5'   # 小额测试
        })

        # 转换为字典并返回
        data = df_renamed.to_dict(orient='records')
        print(f"返回真实数据，条数: {len(data)}")
        return json({"data": data})
    except Exception as e:
        print(f"Error in transmit: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回错误信息，而不是使用测试数据
        return json({"error": f"处理数据时出错: {str(e)}"}, status=500)



# 启动服务
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000)
