{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1748923301732}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\n:action=\"uploadUrl\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"fileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击上传</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"fileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '处理中...' : '导入数据' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"fileList.length === 0\"\r\n@click=\"clearFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploadProgress\"\r\n:status=\"uploadProgress === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n>\r\n</el-progress>\r\n<p class=\"progress-text\">{{ progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"category\" label=\"商品品类\" width=\"120\" />\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"orderTime\" label=\"下单时间\" width=\"100\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentTime\" label=\"支付时间\" width=\"100\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      fileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      progressText: '',\r\n      uploadUrl: 'http://127.0.0.1:8000/upload-excel', // 后端上传接口\r\n\r\n      // 异常数据列表\r\n      exceptionList: [\r\n        {\r\n          orderNo: 'DD20240715001',\r\n          category: '电子产品',\r\n          specs: '笔记本电脑/16GB 512GB',\r\n          unitPrice: 8999.00,\r\n          quantity: 1,\r\n          totalAmount: 8999.00,\r\n          payerName: '李四',\r\n          idNumber: '310***********5678',\r\n          phone: '13900139000',\r\n          orderDate: '2024-07-15',\r\n          orderTime: '10:15',\r\n          paymentDate: '2024-07-15',\r\n          paymentTime: '10:20',\r\n          logisticsNo: 'WL987654321'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        }\r\n      ],\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户上传文件\r\n    this.exceptionList = []\r\n  },\r\n  methods: {\r\n    // 文件选择变化处理\r\n    handleFileChange(file, fileList) {\r\n      this.fileList = fileList\r\n      console.log('文件列表更新:', fileList)\r\n    },\r\n\r\n    // 文件移除处理\r\n    handleFileRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    // 上传前验证\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    // 清空文件列表\r\n    clearFiles() {\r\n      this.fileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空文件列表')\r\n    },\r\n\r\n    // 处理文件上传\r\n    async handleUpload() {\r\n      if (this.fileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.progressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.fileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.progressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 发送请求到后端\r\n        const response = await axios.post(this.uploadUrl, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          },\r\n          timeout: 60000 // 60秒超时\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        // 处理响应数据\r\n        if (response.data && response.data.success) {\r\n          this.exceptionList = response.data.exceptions || []\r\n          this.$message.success(`成功处理 ${this.fileList.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)\r\n        } else {\r\n          throw new Error(response.data.message || '数据处理失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.code === 'ECONNABORTED') {\r\n          this.$message.error('请求超时，请检查网络连接或文件大小')\r\n        } else if (error.response) {\r\n          this.$message.error(`上传失败: ${error.response.data.message || error.message}`)\r\n        } else {\r\n          this.$message.error(`上传失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传容器样式 */\r\n.upload-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.upload-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n/* 上传区域样式优化 */\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  height: 600px;\r\n  position: relative;\r\n}\r\n\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 文件列表样式优化 */\r\n.upload-demo .el-upload-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.upload-demo .el-upload-list__item {\r\n  padding: 8px 10px;\r\n  margin-top: 5px;\r\n  background: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}