import pandas as pd

def detectTrade(paths):
    # 1. 读取指定列（列号固定）
    result_final = {}
    for path in paths:
        df = pd.read_excel(path, skiprows=1, usecols=[0, 4, 6, 12])
        df.columns = ['订单号', '支付人姓名', '支付人身份证号', '物流单号']

        result = {}

        # 1️⃣ 同一姓名多个身份证号
        name_to_ids = df.groupby('支付人姓名')['支付人身份证号'].nunique()
        conflicted_names = name_to_ids[name_to_ids > 1].index
        result['同一姓名多个身份证'] = df[df['支付人姓名'].isin(conflicted_names)]

        # 2️⃣ 同一身份证号多个姓名
        id_to_names = df.groupby('支付人身份证号')['支付人姓名'].nunique()
        conflicted_ids = id_to_names[id_to_names > 1].index
        result['同一身份证多个姓名'] = df[df['支付人身份证号'].isin(conflicted_ids)]

        # 3️⃣ 物流单号重复（排除完全重复：仅保留不同订单号/姓名组合的）
        # 3.1 筛出重复物流单号
        logistics_counts = df['物流单号'].dropna().value_counts()
        duplicated_logistics = logistics_counts[logistics_counts > 1].index

        # 3.2 找出这些物流单号对应的多种组合（订单号 + 姓名）是否一致
        df_dup = df[df['物流单号'].isin(duplicated_logistics)].copy()
        combo_counts = df_dup.groupby('物流单号')[['订单号', '支付人姓名']].nunique()

        # 筛出 订单号或姓名存在多个值的物流单号
        conflict_logistics = combo_counts[
            (combo_counts['订单号'] > 1) | (combo_counts['支付人姓名'] > 1)
        ].index

        result['物流单号重复'] = df[df['物流单号'].isin(conflict_logistics)]

        # 4️⃣ 订单号关联多个身份证号
        order_to_ids = df.groupby('订单号')['支付人身份证号'].nunique()
        multi_id_orders = order_to_ids[order_to_ids > 1].index
        result['订单号多个身份证'] = df[df['订单号'].isin(multi_id_orders)]

        for key in result:
            if key not in result_final:
                result_final[key] = result[key].reset_index(drop=True)
            else:
                result_final[key] = pd.concat([
                    result_final[key].reset_index(drop=True),
                    result[key].reset_index(drop=True)
                ], axis=0)

    return {
        key: df.fillna("").to_dict(orient="records")  # NaN 转为 ""，避免 JSON 报错
        for key, df in result_final.items()
    }



path = [rf"D:\\2025大创_地下田庄\\server3.3\data\交易付款单和明细\CKJRM2209010004\2209010001.xlsx"]

result = detectTrade(path)

print(result)