{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\charts.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\charts.js", "mtime": 1748922891796}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1731756266000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Layout", "chartsRouter", "path", "component", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "noCache", "hidden"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/router/modules/charts.js"], "sourcesContent": ["/** When your routing table is too long, you can split it into small modules**/\n\nimport Layout from '@/layout'\n\nconst chartsRouter = {\n  path: '/charts',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'Charts',\n  meta: {\n    title: '数据监测',\n    icon: 'chart'\n  },\n  children: [\n    // {\n    //   path: 'keyboard',\n    //   component: () => import('@/views/charts/keyboard'),\n    //   name: 'KeyboardChart',\n    //   meta: { title: 'Keyboard Chart', noCache: true }\n    // },\n    // {\n    //   path: 'line',\n    //   component: () => import('@/views/charts/line'),\n    //   name: 'LineChart',\n    //   meta: { title: 'Line Chart', noCache: true }\n    // },\n    // {\n    //   path: 'mix-chart',\n    //   component: () => import('@/views/charts/mix-chart'),\n    //   name: 'MixChart',\n    //   meta: { title: 'Mix Chart', noCache: true }\n    // },\n    {\n      path: 'KnowledgeGraph',\n      component: () => import('@/views/charts/knowledgeGraph.vue'),\n      name: 'KnowledgeGraph',\n      meta: { title: '人物关系网', noCache: true }\n    },\n    // {\n    //   path: 'perKnowledgeGraph',\n    //   component: () => import('@/views/charts/test.vue'),\n    //   name: 'Test',\n    //   meta: { title: '单级人物关系网', noCache: true }\n    // },\n    {\n      path: 'lineChart',\n      component: () => import('@/views/charts/lineChart.vue'),\n      name: 'LineChart',\n      meta: { title: '交易流水分析', noCache: true }\n    },\n    {\n      path: 'RateGraph',\n      component: () => import('@/views/charts/RateGraph.vue'),\n      name: 'RateGraph',\n      meta: { title: '嫌疑排名', noCache: true }\n    },\n    {\n      path: 'transmit-detail',\n      component: () => import('@/views/charts/TransmitDetail.vue'),\n      name: 'TransmitDetail',\n      meta: { title: '详细信息', noCache: true },\n      hidden: true\n    },\n    {\n      path: 'order-exception',\n      component: () => import('@/views/charts/OrderException.vue'),\n      name: 'OrderException',\n      meta: { title: '物流异常', noCache: true }\n    }\n  ]\n}\n\nexport default chartsRouter\n"], "mappings": ";;;;AAAA;;AAEA,OAAOA,MAAM,MAAM,UAAU;AAE7B,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACEP,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DT,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEQ,OAAO,EAAE;IAAK;EACxC,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;IACEb,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDT,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEQ,OAAO,EAAE;IAAK;EACzC,CAAC,EACD;IACEb,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDT,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEQ,OAAO,EAAE;IAAK;EACvC,CAAC,EACD;IACEb,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DT,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEQ,OAAO,EAAE;IAAK,CAAC;IACtCC,MAAM,EAAE;EACV,CAAC,EACD;IACEd,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DT,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEQ,OAAO,EAAE;IAAK;EACvC,CAAC;AAEL,CAAC;AAED,eAAed,YAAY", "ignoreList": []}]}