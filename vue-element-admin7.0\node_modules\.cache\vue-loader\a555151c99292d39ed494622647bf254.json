{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749131212448}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzDQogICAgICB1cGxvYWRGaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsDQogICAgICB1cGxvYWRQcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDmlbDmja7ooajpgInmi6nnm7jlhbMNCiAgICAgIGF2YWlsYWJsZVRhYmxlczogWw0KICAgICAgICAvLyDmt7vliqDkuIDkupvmtYvor5XmlbDmja7vvIzku6XpmLLlkI7nq6/msqHmnInmlbDmja7ml7bkuZ/og73nnIvliLDooajmoLwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIHRhYmxlTmFtZTogJ3Rlc3RfdGFibGVfMScsDQogICAgICAgICAgY3JlYXRlRGF0ZTogJzIwMjQtMTItMjAgMTA6MDA6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAxMDAwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIHRhYmxlTmFtZTogJ3Rlc3RfdGFibGVfMicsDQogICAgICAgICAgY3JlYXRlRGF0ZTogJzIwMjQtMTItMjAgMTE6MDA6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAyMDAwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfQ0KICAgICAgXSwgLy8g5LuO5ZCO56uv5Yqo5oCB5Yqg6L29DQogICAgICBzZWxlY3RlZFRhYmxlczogW10sDQogICAgICBsb2FkaW5nRmlsZXM6IGZhbHNlLA0KICAgICAgcHJvY2Vzc2luZzogZmFsc2UsDQogICAgICBwcm9jZXNzUHJvZ3Jlc3M6IDAsDQogICAgICBwcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDlvILluLjmlbDmja7liJfooagNCiAgICAgIGV4Y2VwdGlvbkxpc3Q6IFtdLCAvLyDku47lkI7nq6/lvILluLjmo4DmtYvojrflj5YNCiAgICAgIHNjcm9sbENvbnRhaW5lcjogbnVsbA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJbml7bmuIXnqbrlvILluLjmlbDmja7liJfooajvvIznrYnlvoXnlKjmiLfpgInmi6nmlofku7YNCiAgICB0aGlzLmV4Y2VwdGlvbkxpc3QgPSBbXQ0KICAgIC8vIOWKoOi9veWPr+eUqOaWh+S7tuWIl+ihqA0KICAgIHRoaXMubG9hZEF2YWlsYWJsZUZpbGVzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaWh+S7tuS4iuS8oOebuOWFs+aWueazlQ0KICAgIGhhbmRsZUZpbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QgPSBmaWxlTGlzdA0KICAgICAgY29uc29sZS5sb2coJ+S4iuS8oOaWh+S7tuWIl+ihqOabtOaWsDonLCBmaWxlTGlzdCkNCiAgICB9LA0KDQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IGZpbGVMaXN0DQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25bey56e76ZmkOicsIGZpbGUubmFtZSkNCiAgICB9LA0KDQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwNCiAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwDQoNCiAgICAgIGlmICghaXNFeGNlbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDEwTSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4cxME1CIScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlIC8vIOmYu+atouiHquWKqOS4iuS8oO+8jOaJi+WKqOaOp+WItg0KICAgIH0sDQoNCiAgICBjbGVhclVwbG9hZEZpbGVzKCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCkNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF56m65LiK5Lyg5paH5Lu25YiX6KGoJykNCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlVXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMudXBsb2FkRmlsZUxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5LiK5Lyg55qERXhjZWzmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMA0KICAgICAgdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAn5YeG5aSH5LiK5Lyg5paH5Lu2Li4uJw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQoNCiAgICAgICAgLy8g5re75Yqg5omA5pyJ5paH5Lu25YiwRm9ybURhdGENCiAgICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdC5mb3JFYWNoKChmaWxlSXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGVzJywgZmlsZUl0ZW0ucmF3KQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnVwbG9hZFByb2dyZXNzIDwgOTApIHsNCiAgICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgKz0gTWF0aC5yYW5kb20oKSAqIDEwDQogICAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9IGDmraPlnKjkuIrkvKDmlofku7YuLi4gJHtNYXRoLnJvdW5kKHRoaXMudXBsb2FkUHJvZ3Jlc3MpfSVgDQogICAgICAgICAgfQ0KICAgICAgICB9LCAyMDApDQoNCiAgICAgICAgLy8g6L+Z6YeM5bCG5p2l6L+e5o6l5ZCO56uvQVBJ5LiK5Lyg5paH5Lu2DQogICAgICAgIC8vIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL3VwbG9hZC1maWxlcycsIGZvcm1EYXRhLCB7DQogICAgICAgIC8vICAgaGVhZGVyczogew0KICAgICAgICAvLyAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJw0KICAgICAgICAvLyAgIH0sDQogICAgICAgIC8vICAgdGltZW91dDogNjAwMDANCiAgICAgICAgLy8gfSkNCg0KICAgICAgICAvLyDmqKHmi5/kuIrkvKDml7bpl7QNCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKQ0KDQogICAgICAgIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCkNCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDEwMA0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICfmlofku7bkuIrkvKDlrozmiJDvvIEnDQoNCiAgICAgICAgLy8g5qih5ouf5LiK5Lyg5oiQ5Yqf77yM5re75Yqg5Yiw5Y+v55So5pWw5o2u6KGo5YiX6KGoDQogICAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QuZm9yRWFjaCgoZmlsZUl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgY29uc3QgbmV3VGFibGUgPSB7DQogICAgICAgICAgICBpZDogdGhpcy5hdmFpbGFibGVUYWJsZXMubGVuZ3RoICsgaW5kZXggKyAxLA0KICAgICAgICAgICAgdGFibGVOYW1lOiBmaWxlSXRlbS5uYW1lLnJlcGxhY2UoL1wuKHhsc3h8eGxzKSQvaSwgJycpLCAvLyDnp7vpmaTmlofku7bmianlsZXlkI3kvZzkuLrooajlkI0NCiAgICAgICAgICAgIGNyZWF0ZURhdGU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJyksDQogICAgICAgICAgICByZWNvcmRDb3VudDogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNTAwMCkgKyAxMDAsIC8vIOaooeaLn+iusOW9leaVsA0KICAgICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmF2YWlsYWJsZVRhYmxlcy5wdXNoKG5ld1RhYmxlKQ0KICAgICAgICB9KQ0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5LiK5LygICR7dGhpcy51cGxvYWRGaWxlTGlzdC5sZW5ndGh9IOS4quaWh+S7tmApDQogICAgICAgIHRoaXMuY2xlYXJVcGxvYWRGaWxlcygpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfkuIrkvKDlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwDQogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDANCiAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICcnDQogICAgICAgIH0sIDMwMDApDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWPr+eUqOaVsOaNruihqOWIl+ihqA0KICAgIGFzeW5jIGxvYWRBdmFpbGFibGVGaWxlcygpIHsNCiAgICAgIHRoaXMubG9hZGluZ0ZpbGVzID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55So5ZCO56uvQVBJ6I635Y+W5omA5pyJRXhjZWzmlofku7bot6/lvoQNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvZ2V0X2FsbF9UcmFja2luZ051bScpDQogICAgICAgIGNvbnNvbGUubG9nKCflkI7nq6/ov5Tlm57nmoRFeGNlbOaWh+S7tui3r+W+hDonLCByZXNwb25zZS5kYXRhKQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucGF0aHMpIHsNCiAgICAgICAgICAvLyDlsIbmlofku7bot6/lvoTovazmjaLkuLrliY3nq6/mmL7npLrmoLzlvI8NCiAgICAgICAgICB0aGlzLmF2YWlsYWJsZVRhYmxlcyA9IHJlc3BvbnNlLmRhdGEucGF0aHMubWFwKChmaWxlUGF0aCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIC8vIOaPkOWPluaWh+S7tuWQjeS9nOS4uuihqOWQjeaYvuekug0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBmaWxlUGF0aC5zcGxpdCgnXFwnKS5wb3AoKSB8fCBmaWxlUGF0aC5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICBjb25zdCB0YWJsZU5hbWUgPSBmaWxlTmFtZS5yZXBsYWNlKCcueGxzeCcsICcnKSAvLyDnp7vpmaTmianlsZXlkI0NCg0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgaWQ6IGluZGV4ICsgMSwNCiAgICAgICAgICAgICAgdGFibGVOYW1lOiB0YWJsZU5hbWUsIC8vIOaYvuekuuaWh+S7tuWQje+8iOS4jeWQq+aJqeWxleWQje+8iQ0KICAgICAgICAgICAgICBmaWxlUGF0aDogZmlsZVBhdGgsIC8vIOS/neWtmOWujOaVtOi3r+W+hOeUqOS6juWQjuerr+WkhOeQhg0KICAgICAgICAgICAgICBjcmVhdGVEYXRlOiAnMjAyNC0xMi0yMCAxMDowMDowMCcsIC8vIOWQjuerr+ayoeacieaPkOS+m+aXtumXtO+8jOS9v+eUqOm7mOiupOWAvA0KICAgICAgICAgICAgICByZWNvcmRDb3VudDogbnVsbCwgLy8g5ZCO56uv5rKh5pyJ5o+Q5L6b6K6w5b2V5pWwDQogICAgICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5Yqg6L295LqGICR7dGhpcy5hdmFpbGFibGVUYWJsZXMubGVuZ3RofSDkuKpFeGNlbOaWh+S7tmApDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmsqHmnInmib7liLDlj6/nlKjnmoRFeGNlbOaWh+S7ticpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vUV4Y2Vs5paH5Lu25YiX6KGo5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb1FeGNlbOaWh+S7tuWIl+ihqOWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmdGaWxlcyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhkV4Y2Vs5paH5Lu26YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZFRhYmxlcyA9IHNlbGVjdGlvbg0KICAgICAgY29uc29sZS5sb2coJ+W3sumAieaLqUV4Y2Vs5paH5Lu2OicsIHNlbGVjdGlvbikNCiAgICB9LA0KDQogICAgLy8g56e76Zmk5bey6YCJ5oup55qERXhjZWzmlofku7YNCiAgICByZW1vdmVTZWxlY3RlZFRhYmxlKHRhYmxlKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuc2VsZWN0ZWRUYWJsZXMuZmluZEluZGV4KHQgPT4gdC5pZCA9PT0gdGFibGUuaWQpDQogICAgICBpZiAoaW5kZXggPiAtMSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkVGFibGVzLnNwbGljZShpbmRleCwgMSkNCiAgICAgIH0NCiAgICAgIC8vIOWQjOaXtuabtOaWsOihqOagvOmAieaLqeeKtuaAgQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZVJlZiA9IHRoaXMuJHJlZnMudGFibGVMaXN0DQogICAgICAgIGlmICh0YWJsZVJlZikgew0KICAgICAgICAgIHRhYmxlUmVmLnRvZ2dsZVJvd1NlbGVjdGlvbih0YWJsZSwgZmFsc2UpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOa4heepuumAieaLqQ0KICAgIGNsZWFyU2VsZWN0aW9uKCkgew0KICAgICAgdGhpcy5zZWxlY3RlZFRhYmxlcyA9IFtdDQogICAgICAvLyDmuIXnqbrooajmoLzpgInmi6kNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3QgdGFibGVSZWYgPSB0aGlzLiRyZWZzLnRhYmxlTGlzdA0KICAgICAgICBpZiAodGFibGVSZWYpIHsNCiAgICAgICAgICB0YWJsZVJlZi5jbGVhclNlbGVjdGlvbigpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3sua4heepukV4Y2Vs5paH5Lu26YCJ5oupJykNCiAgICB9LA0KICAgIGFzeW5jIHByb2Nlc3NTZWxlY3RlZFRhYmxlcygpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkVGFibGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWkhOeQhueahEV4Y2Vs5paH5Lu2JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMucHJvY2Vzc2luZyA9IHRydWUNCiAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMA0KICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAn5byA5aeL5aSE55CGRXhjZWzmlofku7YuLi4nDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOi/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnByb2Nlc3NQcm9ncmVzcyA8IDgwKSB7DQogICAgICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTANCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRTdGVwID0gTWF0aC5mbG9vcih0aGlzLnByb2Nlc3NQcm9ncmVzcyAvIDI1KQ0KICAgICAgICAgICAgY29uc3Qgc3RlcHMgPSBbJ+ato+WcqOivu+WPlkV4Y2Vs5paH5Lu2Li4uJywgJ+ato+WcqOWQiOW5tuaVsOaNri4uLicsICfmraPlnKjliIbmnpDlvILluLguLi4nLCAn5aSE55CG5LitLi4uJ10NCiAgICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gc3RlcHNbY3VycmVudFN0ZXBdIHx8ICflpITnkIbkuK0uLi4nDQogICAgICAgICAgfQ0KICAgICAgICB9LCA1MDApDQoNCiAgICAgICAgLy8g6LCD55So5ZCO56uv5byC5bi45qOA5rWL5o6l5Y+jDQogICAgICAgIGNvbnN0IGZpbGVQYXRocyA9IHRoaXMuc2VsZWN0ZWRUYWJsZXMubWFwKHQgPT4gdC5maWxlUGF0aCkNCiAgICAgICAgY29uc29sZS5sb2coJ+WPkemAgeWIsOWQjuerr+eahOaWh+S7tui3r+W+hDonLCBmaWxlUGF0aHMpDQoNCiAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAn5q2j5Zyo6LCD55So5ZCO56uv5YiG5p6Q5o6l5Y+jLi4uJw0KDQogICAgICAgIC8vIOecn+ato+iwg+eUqOWQjuerr0FQSQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoJ2h0dHA6Ly8xMjcuMC4wLjE6ODAwMC9nZXRfc3VzX1RyYWNraW5nTnVtJywgew0KICAgICAgICAgIGZpbGVuYW1lczogZmlsZVBhdGhzDQogICAgICAgIH0pDQoNCiAgICAgICAgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKQ0KICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyA9IDEwMA0KICAgICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICfmlbDmja7lpITnkIblrozmiJDvvIEnDQoNCiAgICAgICAgY29uc29sZS5sb2coJ+WQjuerr+i/lOWbnueahOW8guW4uOajgOa1i+e7k+aenDonLCByZXNwb25zZS5kYXRhKQ0KDQogICAgICAgIC8vIOWkhOeQhuWQjuerr+i/lOWbnueahOW8guW4uOaVsOaNrg0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIGNvbnN0IGV4Y2VwdGlvbkxpc3QgPSBbXQ0KDQogICAgICAgICAgLy8g6YGN5Y6G5ZCO56uv6L+U5Zue55qE5ZCE56eN5byC5bi457G75Z6LDQogICAgICAgICAgT2JqZWN0LmtleXMocmVzcG9uc2UuZGF0YSkuZm9yRWFjaChleGNlcHRpb25UeXBlID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGV4Y2VwdGlvbnMgPSByZXNwb25zZS5kYXRhW2V4Y2VwdGlvblR5cGVdDQogICAgICAgICAgICBpZiAoZXhjZXB0aW9ucyAmJiBleGNlcHRpb25zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgZXhjZXB0aW9ucy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgICAgIC8vIOagueaNruWQjuerr+i/lOWbnueahOaVsOaNrue7k+aehOi9rOaNouS4uuWJjeerr+aYvuekuuagvOW8jw0KICAgICAgICAgICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IHsNCiAgICAgICAgICAgICAgICAgIG9yZGVyTm86IGl0ZW1bJ+iuouWNleWPtyddIHx8IGDlvILluLgtJHtEYXRlLm5vdygpfS0ke2luZGV4fWAsDQogICAgICAgICAgICAgICAgICBjYXRlZ29yeTogZXhjZXB0aW9uVHlwZSwgLy8g5byC5bi457G75Z6L5L2c5Li65YiG57G7DQogICAgICAgICAgICAgICAgICBzcGVjczogYCR7ZXhjZXB0aW9uVHlwZX3lvILluLhgLA0KICAgICAgICAgICAgICAgICAgdW5pdFByaWNlOiAwLA0KICAgICAgICAgICAgICAgICAgcXVhbnRpdHk6IDEsDQogICAgICAgICAgICAgICAgICB0b3RhbEFtb3VudDogMCwNCiAgICAgICAgICAgICAgICAgIHBheWVyTmFtZTogaXRlbVsn5pSv5LuY5Lq65aeT5ZCNJ10gfHwgJ+acquefpScsDQogICAgICAgICAgICAgICAgICBpZE51bWJlcjogaXRlbVsn5pSv5LuY5Lq66Lqr5Lu96K+B5Y+3J10gfHwgJ+acquefpScsDQogICAgICAgICAgICAgICAgICBwaG9uZTogJ+acquaPkOS+mycsDQogICAgICAgICAgICAgICAgICBvcmRlckRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLA0KICAgICAgICAgICAgICAgICAgb3JkZXJUaW1lOiBuZXcgRGF0ZSgpLnRvVGltZVN0cmluZygpLnNwbGl0KCcgJylbMF0sDQogICAgICAgICAgICAgICAgICBwYXltZW50RGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sDQogICAgICAgICAgICAgICAgICBwYXltZW50VGltZTogbmV3IERhdGUoKS50b1RpbWVTdHJpbmcoKS5zcGxpdCgnICcpWzBdLA0KICAgICAgICAgICAgICAgICAgbG9naXN0aWNzTm86IGl0ZW1bJ+eJqea1geWNleWPtyddIHx8ICfmnKrnn6UnLA0KICAgICAgICAgICAgICAgICAgZXhjZXB0aW9uVHlwZTogZXhjZXB0aW9uVHlwZSAvLyDmt7vliqDlvILluLjnsbvlnovlrZfmrrUNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgZXhjZXB0aW9uTGlzdC5wdXNoKGV4Y2VwdGlvbikNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgdGhpcy5leGNlcHRpb25MaXN0ID0gZXhjZXB0aW9uTGlzdA0KDQogICAgICAgICAgaWYgKGV4Y2VwdGlvbkxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/lpITnkIYgJHt0aGlzLnNlbGVjdGVkVGFibGVzLmxlbmd0aH0g5LiqRXhjZWzmlofku7bvvIzlj5HnjrAgJHtleGNlcHRpb25MaXN0Lmxlbmd0aH0g5p2h5byC5bi45pWw5o2uYCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKGDmiJDlip/lpITnkIYgJHt0aGlzLnNlbGVjdGVkVGFibGVzLmxlbmd0aH0g5LiqRXhjZWzmlofku7bvvIzmnKrlj5HnjrDlvILluLjmlbDmja5gKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+WQjuerr+i/lOWbnuaVsOaNruagvOW8j+W8guW4uCcpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WkhOeQhuWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJycNCg0KICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2UpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlpITnkIblpLHotKU6ICR7ZXJyb3IucmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3IucmVzcG9uc2UuZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlfWApDQogICAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVxdWVzdCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOi/nuaOpeWksei0pe+8jOivt+ajgOafpeWQjuerr+acjeWKoeaYr+WQpuWQr+WKqCcpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5aSE55CG5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5wcm9jZXNzaW5nID0gZmFsc2UNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAnJw0KICAgICAgICB9LCAzMDAwKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVTY3JvbGwoZXZlbnQpIHsNCiAgICAgIC8vIOWkhOeQhua7muWKqOS6i+S7tg0KICAgICAgY29uc29sZS5sb2coJ1Njcm9sbGluZy4uLicsIGV2ZW50KQ0KICAgIH0sDQoNCiAgICAvLyDmoLnmja7lvILluLjnsbvlnovov5Tlm57lr7nlupTnmoTmoIfnrb7popzoibINCiAgICBnZXRFeGNlcHRpb25UeXBlQ29sb3IoZXhjZXB0aW9uVHlwZSkgew0KICAgICAgY29uc3QgY29sb3JNYXAgPSB7DQogICAgICAgICflkIzkuIDlp5PlkI3lpJrkuKrouqvku73or4EnOiAnZGFuZ2VyJywNCiAgICAgICAgJ+WQjOS4gOi6q+S7veivgeWkmuS4quWnk+WQjSc6ICd3YXJuaW5nJywNCiAgICAgICAgJ+eJqea1geWNleWPt+mHjeWkjSc6ICdpbmZvJywNCiAgICAgICAgJ+iuouWNleWPt+WkmuS4qui6q+S7veivgSc6ICdzdWNjZXNzJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNvbG9yTWFwW2V4Y2VwdGlvblR5cGVdIHx8ICdwcmltYXJ5Jw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到服务器</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器中选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"exceptionType\" label=\"异常类型\" width=\"150\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"getExceptionTypeColor(row.exceptionType)\" size=\"small\">\r\n{{ row.exceptionType }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // 数据表选择相关\r\n      availableTables: [\r\n        // 添加一些测试数据，以防后端没有数据时也能看到表格\r\n        {\r\n          id: 1,\r\n          tableName: 'test_table_1',\r\n          createDate: '2024-12-20 10:00:00',\r\n          recordCount: 1000,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          tableName: 'test_table_2',\r\n          createDate: '2024-12-20 11:00:00',\r\n          recordCount: 2000,\r\n          status: 'available'\r\n        }\r\n      ], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 这里将来连接后端API上传文件\r\n        // const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n        //   headers: {\r\n        //     'Content-Type': 'multipart/form-data'\r\n        //   },\r\n        //   timeout: 60000\r\n        // })\r\n\r\n        // 模拟上传时间\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 模拟上传成功，添加到可用数据表列表\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          const newTable = {\r\n            id: this.availableTables.length + index + 1,\r\n            tableName: fileItem.name.replace(/\\.(xlsx|xls)$/i, ''), // 移除文件扩展名作为表名\r\n            createDate: new Date().toLocaleString('zh-CN'),\r\n            recordCount: Math.floor(Math.random() * 5000) + 100, // 模拟记录数\r\n            status: 'available'\r\n          }\r\n          this.availableTables.push(newTable)\r\n        })\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 根据后端返回的数据结构转换为前端显示格式\r\n                const exception = {\r\n                  orderNo: item['订单号'] || `异常-${Date.now()}-${index}`,\r\n                  category: exceptionType, // 异常类型作为分类\r\n                  specs: `${exceptionType}异常`,\r\n                  unitPrice: 0,\r\n                  quantity: 1,\r\n                  totalAmount: 0,\r\n                  payerName: item['支付人姓名'] || '未知',\r\n                  idNumber: item['支付人身份证号'] || '未知',\r\n                  phone: '未提供',\r\n                  orderDate: new Date().toISOString().split('T')[0],\r\n                  orderTime: new Date().toTimeString().split(' ')[0],\r\n                  paymentDate: new Date().toISOString().split('T')[0],\r\n                  paymentTime: new Date().toTimeString().split(' ')[0],\r\n                  logisticsNo: item['物流单号'] || '未知',\r\n                  exceptionType: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}