import pymysql

def get_forms(connect_info = None):
    """
    查找数据库中所有表名
    :return: 所用表名构成的列表tables
    """
    backup_connection = {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "root",
        "database": "digit_check"
    }
    connect_dic = connect_info if connect_info else backup_connection
    connection = pymysql.connect(**connect_dic)
    cursor = connection.cursor()
    cursor.execute("SHOW TABLES")
    tables = [table[0] for table in cursor.fetchall()]
    print("getForms : ",tables)
    if 'log' in tables:
        tables.remove('log')
    return tables