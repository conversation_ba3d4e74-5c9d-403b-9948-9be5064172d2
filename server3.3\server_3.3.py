import os
from sanic import Sanic, response
from sanic.response import json, text
import pymysql
from collections import defaultdict
import math
from copy import deepcopy
import traceback
import pandas as pd
import dask.dataframe as dd

#以下为自定义函数调用
from ExceldataCleaning import data_trim1,data_trim2
from Json2Target_form import load_to_mysql_with_duplicate_checking
import anomaly_system_strict_match.run
from logicalFilter import logical_filter
from getForms import get_forms
from anomaly_system_strict_match import run #机器学习调用
import lineGraphDataGen
from detectTrade import detectTrade

connect_info = {
    "host": "localhost",
    "port": 3306,
    "user":"root",
    "password":"root",
    "database":"digit_check"
}

#创建Sainc实例
app = Sanic("SimpleServer")

{# def load_to_mysql(json_data):
#     # 这个函数可能没用了，因为2.0版更新成了load_to_mysql_with_duplicate_checking
#     suc_line = 0
#     fail_line = 0
#     data = pd.DataFrame(json_data)
#     if '交易日期' not in data.columns:
#         data = data_trim1(data)
#     else:
#         data = data_trim2(data)
#     data = data[['交易户名', '交易卡号', '交易账号', '交易时间', '收付标志', '交易金额', '交易余额', '交易币种', '对手账号', '对手户名', '交易网点名称', '对手开户银行', '备注', '摘要说明', 'IP地址', 'MAC地址']]
#     data = data.astype(str)
#     data = data.applymap(lambda x: None if pd.isna(x) or x == 'nan' else x)
#     connection = pymysql.connect(host="localhost", user="root", password="123456", database="digit_check")
#     cursor = connection.cursor()
#
#     create_table_query = f"""
#         CREATE TABLE IF NOT EXISTS data_to_mysql (
#             `交易户名` VARCHAR(64) NOT NULL,
#             `交易卡号` VARCHAR(64) NOT NULL,
#             `交易账号` varchar(64),
#             `交易时间` DATETIME,
#             `收付标志` VARCHAR(64),
#             `交易金额` DECIMAL(18, 2),
#             `交易余额` DECIMAL(18,2),
#             `交易币种` varchar(64),
#             `对手账号` VARCHAR(64),
#             `对手户名` VARCHAR(64),
#             `交易网点名称` VARCHAR(64),
#             `对手开户银行` varchar(64),
#             `备注` varchar(512),
#             `摘要说明` varchar(512),
#             `IP地址` varchar(512),
#             `MAC地址` varchar(512)
#         );
#         """
#
#     insert_query = f"""
#         INSERT INTO data_to_mysql (
#             交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种,
#             对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, IP地址, MAC地址
#         )
#         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
#         """
#
#     cursor.execute(create_table_query)
#     for _, row in data.iterrows():
#
#         if row['交易户名'] is None or row['交易卡号'] is None or row['交易时间'].strip() is None or row['交易金额'] == '':
#             fail_line += 1
#             continue
#         suc_line += 1
#
#         cursor.execute(insert_query, (
#             row['交易户名'],
#             row['交易卡号'],
#             row['交易账号'],  # 新增字段
#             row['交易时间'],
#             row['收付标志'],
#             row['交易金额'],
#             row['交易余额'],  # 新增字段
#             row['交易币种'],  # 新增字段
#             row['对手账号'],
#             row['对手户名'],
#             row['交易网点名称'],
#             row['对手开户银行'],  # 新增字段
#             row['备注'],
#             row['摘要说明'],  # 新增字段
#             row['IP地址'],  # 后加的字段
#             row['MAC地址']  # 后加的字段
#         ))
#
#     connection.commit()
#
#     # 关闭游标和连接
#     cursor.close()
#     connection.close()
#     result = {
#         "success_lines": suc_line,
#         "fail_lines": fail_line
#     }
#
#     return result
}

{# @app.middleware("response")
# async def add_cors_headers(request, response):
#     origin = request.headers.get("Origin")
#     if origin:
#         response.headers["Access-Control-Allow-Origin"] = origin
#     response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
#     response.headers["Access-Control-Allow-Headers"] = "Content-Type"
}
@app.middleware("response")
async def add_cors_headers(request, response):
    # 获取请求的 Origin
    origin = request.headers.get("Origin")
    if origin:
        # 动态设置允许的 Origin
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Vary"] = "Origin"  # 告诉浏览器不同 Origin 会返回不同结果
    else:
        response.headers["Access-Control-Allow-Origin"] = "*"  # 如果没有 Origin，允许所有

    # 添加其他 CORS 必要头信息
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    response.headers["Access-Control-Allow-Credentials"] = "true"  # 如果需要支持 cookies

    # 处理 OPTIONS 请求
    if request.method == "OPTIONS":
        response.status = 204  # OPTIONS 请求需要返回 204 无内容状态码
        response.body = b""  # 不需要返回任何内容
# 中间件捕获所有请求
@app.middleware("request")
async def log_request(request):
    # 输出请求信息
    print(f"Request received: {request.method} {request.path}")
    print(f"Headers: {request.headers}")
    print(f"Body: {request.body.decode('utf-8', errors='ignore') if request.body else 'No body'}")


@app.route("/hello", methods=["GET"])
async def hello(request):
    data = {"message": "Hello, this is a Sanic server.", "status": "success"}
    return json(data)


@app.route("/transaction_history", methods=["POST"])
async def transaction_history(request):
    """
    根据用户提交的用户名、时间区间、时间粒度等参数，从数据库中查询用户及其对手方的交易记录，按指定时间粒度进行 资金流入/流出统计，并返回结构化的统计数据和相关账户列表。
    查对应用户的流水
    username: 交易人姓名（必填）

startDate, endDate: 查询起止时间

timeScale: 统计粒度（year/month/day/hour）

selectedAccounts: 选定卡号集合（可选）

tableName: 查询的数据库表名，默认为 "测试数据"
    :param request:
    :return:
    """
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    data = request.json
    name = data.get("username")
    startDate = data.get("startDate")
    endDate = data.get("endDate")
    timeScale = data.get("timeScale")
    selectedAccounts = data.get("selectedAccounts", None)
    table_name = data.get("tableName") or "测试数据"
    records = None
    select_query = f"""
        select distinct 交易卡号 from {table_name} where 交易户名 = %s;
    """
    cursor.execute(select_query,(name))
    accounts = cursor.fetchall()
    accounts = [account[0] for account in accounts]

    select_query = f"""
        select distinct 对手账号 from {table_name} where 对手户名 = %s;
    """
    cursor.execute(select_query,(name))
    accounts_opponent = cursor.fetchall()
    accounts_opponent = [account[0] for account in accounts_opponent]
    accounts = accounts + accounts_opponent

    if len(selectedAccounts) == 0:
        selectedAccounts = accounts
    for account in selectedAccounts:
        select_query = f"""
            select 交易时间,收付标志,交易金额,交易户名,对手户名 from {table_name} where (交易卡号 = %s or 对手账号 = %s) and 交易时间 between %s and %s;
        """
        cursor.execute(select_query,(account,account,startDate, endDate))
        results = cursor.fetchall()
        if records is None:
            records = [list(result) for result in results]
        else:
            records.extend(list(result) for result in results)
    print(selectedAccounts)
    print(records)
    deal = defaultdict(lambda: [0,0,0])
    # 按天统计
    cursor.close()
    connection.close()
    for dt, status, money,username,username_opponent in records:
        year = dt.year
        money = abs(float(money))
        if timeScale == 'year':
            if status == '进':
                if username == name:
                    deal[year][0] += money
                    deal[year][1] += money
                if username_opponent == name:
                    deal[year][0] += money
                    deal[year][2] += money
            else:
                if username == name:
                    deal[year][0] += money
                    deal[year][2] += money
                if username_opponent == name:
                    deal[year][0] += money
                    deal[year][1] += money
        elif timeScale == 'month':
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}"][1] += money
        elif timeScale == 'day':
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}"][1] += money
        else:
            if status == '进':
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][1] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][2] += money
            else:
                if username == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][2] += money
                if username_opponent == name:
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][0] += money
                    deal[f"{dt.year}{dt.month:02d}{dt.day:02d}{dt.hour:02d}"][1] += money
    # 使用列表推导式
    # 使用 for 循环遍历字典，插入键到对应的值中
    for key, value in deal.items():
        value.insert(0, key)
    deal = [value for key, value in deal.items()]
    return_dict = {"deal": deal, "accounts": accounts}
    print(return_dict)
    return json(return_dict)


@app.route("/all_transaction_history", methods=["POST"])
async def all_transaction_history(request):  # 查数据表用
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    data = request.json
    account = data.get("account", None)
    name = data.get("name", None)
    page = data.get("page", None)
    limit = data.get("limit", None)
    keywords = data.get('remark', None)
    table_name = data.get("tableName") or "测试数据"
    opname = data.get("opponent", None)
    select_condition = ""
    if name is not None:
        select_condition = select_condition + "交易户名 = '" + name + "'"
    if opname is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "对手户名 = '" + opname + "'"
    if account is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "交易卡号 = " + str(account)
    if keywords is not None:
        if select_condition != "":
            select_condition += " and "
        keys = list(keywords)
        keywords = '%' + '%'.join(keys) + '%'
        select_condition = select_condition + "(备注 LIKE '" + keywords + "' or 摘要说明 LIKE '" + keywords + "')"
    if select_condition != "":
        select_condition += ";"
        query = f"""
            SELECT *
            FROM {table_name}
            WHERE {select_condition}
            """
    else:
        query = f"""
            SELECT *
            FROM {table_name}
            """
    cursor.execute(query)
    results = cursor.fetchall()
    # print(results)
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: (x[5] is None, x[5] or 0), reverse=True)
    for result in results_list:
        result[3] = result[3].isoformat()
        result[5] = str(result[5])
        result[6] = str(result[6])
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    return json(return_dict)

@app.route("/find_same", methods=["POST"])
async def find_some(request):  # 这个函数后来没用到吧
    data = request.json
    persons1_list = data.get('obj1')
    persons2_list = data.get('obj2')
    num = data.get('num')
    table_name = data.get("tableName") or "测试数据"

    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()

    obj1_placeholder = ", ".join(["%s"] * len(persons1_list))
    obj2_placeholder = ", ".join(["%s"] * len(persons2_list))
    query_relation = f'''select 对手户名,count(*), sum(金额1) as 金额
                        from(
                            select 交易户名, 对手户名, count(*),sum(交易金额) as 金额1
                            from {table_name}
                            where 交易户名 in ({obj1_placeholder})
                            group by 交易户名, 对手户名) as s
                        where 交易户名 in ({obj2_placeholder}) and 交易户名 is not null
                        group by 对手户名
                        order by 金额 desc
                        limit {num} '''
    cursor.execute(query_relation, persons1_list + persons2_list)
    sameRivalName = cursor.fetchall()
    cursor.close()
    connection.close()

    sameRivalName = [list(relation) for relation in sameRivalName]
    for relation in sameRivalName:
        relation[2] = str(relation[2])
    return (json(sameRivalName))

@app.route("/find_relation", methods=["POST"])
async def find_relation(request):
    data = request.json
    name = data.get('username')
    num = data.get('minAmount')
    time = data.get('dateRange')
    table_name = data.get("tableName") or "测试数据"
    if num is None:
        num = 1000000
    else:
        try:
            num = float(num)
        except:
            num = 1000000
    time_condition = ""
    if time and len(time) == 2:
       start_time = time[0]  # "2020-01-01 00:00:00"
       end_time = time[1]    # "2020-01-03 00:00:00"
       time_condition = f" AND 交易时间 >= '{start_time}' AND 交易时间 <= '{end_time}'"
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    query = f"""
    WITH 交易关系 AS  (
        SELECT 交易户名, 对手户名, SUM(交易金额) AS 关系金额
        FROM {table_name}
        WHERE 交易户名 IS NOT NULL 
        AND 对手户名 IS NOT NULL 
        AND 交易金额 IS NOT NULL
        AND 对手户名 != 'None'
        {time_condition}
        GROUP BY 交易户名, 对手户名
        HAVING 关系金额 > %s
        )
        SELECT *
        FROM 交易关系
        WHERE 对手户名 IN (
            SELECT 对手户名
            FROM (
                SELECT 对手户名, COUNT(*) AS 交易次数
                FROM 交易关系
                WHERE 关系金额 > %s
                GROUP BY 对手户名
                HAVING 交易次数 > 1
            ) AS 嫌疑户名
--          
        )
        ORDER BY 关系金额
        LIMIT 1000
        """

    cursor.execute(query, (num,num))

    results = cursor.fetchall()
    total_lines = defaultdict(list)
    total_nodes = defaultdict(dict)
    id = 0
    for result in results:
        # 分配id
        if result[0] not in total_nodes.keys():
            total_node = {'id': id, 'color': "rgba(178, 34, 34, 1)", "width": 0, "height": 0}
            total_nodes[result[0]] = total_node
            id += 1
        if result[1] not in total_nodes.keys():
            total_node = {'id': id, 'color': "rgba(178, 34, 34, 1)", "width": 0, "height": 0}
            total_nodes[result[1]] = total_node
            id += 1
        total_line = {'to': result[1], 'text': result[2]}
        total_lines[result[0]].append(total_line)

    # 分配权重 删去无用信息
    lines = []
    nodes = defaultdict(dict)
    usefull_names = []
    if name is None:
        usefull_names = total_nodes.keys()
    else:
        usefull_names = [name]
    id = 0
    while len(usefull_names) != 0:
        mid_usefull_name = []
        for usefull_name in usefull_names:
            if usefull_name not in nodes.keys():
                node = deepcopy(total_nodes[usefull_name])
                node['id'] = id
                nodes[usefull_name] = node
                id += 1
            usefull_lines = total_lines[usefull_name]
            total_money = 0
            for usefull_line in usefull_lines:
                to_name = usefull_line['to']
                if to_name not in nodes.keys():
                    node = deepcopy(total_nodes[to_name])
                    node['id'] = id
                    nodes[to_name] = node
                    id += 1
                    mid_usefull_name.append(to_name)
                line = {'from': f"{nodes[usefull_name]['id']}", 'to': f"{nodes[to_name]['id']}", 'text': f"{float(usefull_line['text'])}"}
                lines.append(line)
                total_money += int(usefull_line['text'])
            nodes[usefull_name]['width'] = math.log10(total_money+10) * 10
            nodes[usefull_name]['height'] = math.log10(total_money+10) * 10
        usefull_names = mid_usefull_name
    nodes_list = []
    for key, value in nodes.items():
        value['text'] = key
        value['id'] = str(value['id'])
        nodes_list.append(value)

    #检查打印：
    print(f"返回节点数: {len(nodes_list)}，边数: {len(lines)}")

    return json({"state": 200, "data": {"nodes": nodes_list, "lines": lines}})


@app.route("/all_relation", methods=["GET"])
async def all_relation(request):
    data = request.json
    table_name = data.get("tableName") or "测试数据"
    num = 1000000
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    query = f"""
    WITH 交易关系 AS  (
        SELECT DISTINCT 交易户名, 对手户名, SUM(交易金额) AS 关系金额
        FROM {table_name}
        WHERE 交易户名 IS NOT NULL 
        AND 对手户名 IS NOT NULL 
        AND 交易金额 IS NOT NULL
        AND 对手户名 != 'None'
        GROUP BY 交易户名, 对手户名
        )
        SELECT *
        FROM 交易关系
        WHERE 对手户名 IN (
            SELECT 对手户名
            FROM (
                SELECT 对手户名, COUNT(*) AS 交易次数
                FROM 交易关系
                WHERE 关系金额 > %s
                GROUP BY 对手户名
            ) AS 嫌疑户名
            WHERE 交易次数 > 1
        )
        """

    cursor.execute(query, num)

    results = cursor.fetchall()
    total_lines = defaultdict(list)
    total_nodes = defaultdict(dict)
    id = 0
    for result in results:
        # 分配id
        if result[0] not in total_nodes.keys():
            total_node = {'id':id,'color':"rgba(178, 34, 34, 1)","width":0, "height": 0}
            total_nodes[result[0]] = total_node
            id += 1
        if result[1] not in total_nodes.keys():
            total_node = {'id':id,'color':"rgba(178, 34, 34, 1)","width":0, "height": 0}
            total_nodes[result[1]] = total_node
            id += 1
        total_line = {'to':result[1],'text':result[2]}
        total_lines[result[0]].append(total_line)

    # 分配权重 删去无用信息
    lines = []
    nodes = defaultdict(dict)
    usefull_names = total_nodes.keys();
    id = 0
    while len(usefull_names) != 0:
        mid_usefull_name = []
        for usefull_name in usefull_names:
            if usefull_name not in nodes.keys():
                node = deepcopy(total_nodes[usefull_name])
                node['id'] = id
                nodes[usefull_name] = node
                id += 1
            usefull_lines = total_lines[usefull_name]
            total_money = 0
            for usefull_line in usefull_lines:
                to_name = usefull_line['to']
                if to_name not in nodes.keys():
                    node = deepcopy(total_nodes[to_name])
                    node['id'] = id
                    nodes[to_name] = node
                    id += 1
                    mid_usefull_name.append(to_name)
                line = {'from':f"{nodes[usefull_name]['id']}",'to':f"{nodes[to_name]['id']}",'text':f"{float(usefull_line['text'])}"}
                lines.append(line)
                total_money += int(usefull_line['text'])
            nodes[usefull_name]['width'] = math.log10(total_money+10)
            nodes[usefull_name]['height'] = math.log10(total_money+10)
        usefull_names = mid_usefull_name
    nodes_list = []
    for key,value in nodes.items():
        value['text'] = key
        value['id'] = str(value['id'])
        nodes_list.append(value)
    return json({"state":200,"data":{"nodes":nodes_list,"lines":lines}})

@app.route("/search_remark", methods=["POST"])
async def search_remark(request):

    data = request.json
    keywords = data.get('keywords')
    page = data.get("page", None)
    limit = data.get("limit", None)
    table_name = data.get("tableName") or "测试数据"

    keys = list(keywords)
    keywords = '%' + '%'.join(keys) + '%'

    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    query = f"""
        SELECT *
        FROM {table_name}
        WHERE 备注 LIKE %s or 摘要说明 LIKE %s
        """

    cursor.execute(query, (keywords,keywords))

    results = cursor.fetchall()
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: x[5], reverse=True)
    for result in results_list:
        result[3] = result[3].isoformat()
        result[5] = str(result[5])
        result[6] = str(result[6])
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    return json(return_dict)

@app.route('/upload', methods=['POST', 'GET'])
async def upload_data(request):
    # 获取JSON数据
    data = request.json
    #target_form = data.get("tableName", "data_to_mysql")#如果包含tabelName就返回，不包含就为默认值”data_to_mysql“
    target_form = data.get("tableName")
    if not target_form:
        target_form = "data_to_mysql"
    #print(data)

    #print("target_form",target_form)

    json_data = data.get("newMember", None)
    username =data.get("user")
    filenames = data.get('filename')
    if json_data:
        #print(json_data)
        result = load_to_mysql_with_duplicate_checking(target_form, json_data,username,filenames,target_form,connect_info=connect_info)
        return json(result)
    else:
        return json({'error': 'No JSON data provided'}, status=400)


@app.route('/all_tables', methods=['POST', 'GET'])
async def get_tables(request):
    return json({"all_tables": get_forms(connect_info)})

@app.route('/logical_filter', methods=['POST', 'GET'])
# @app.route('/upload_tables', methods=['POST', 'GET'])
async def logical_filtering(request):
    data = request.json
    org_form = data.get("org_form", "测试数据")
    logic = data.get("logical", None)
    new_form = data.get("new_form", "default_filtered_table")
    return json({"result": logical_filter(org_form, logic, new_form)})

def drop_table(table_name):
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    drop_query = f"DROP TABLE IF EXISTS {table_name};"
    cursor.execute(drop_query)
    connection.commit()
    cursor.close()

@app.route('/all_log_history', methods=['POST', 'GET'])
async def log_search(request):
    data = request.json
    connection = pymysql.connect(**connect_info)
    cursor = connection.cursor()
    page = data.get("page", None)
    limit = data.get("pagelimit", None)
    time = data.get("time", None)
    user = data.get("username", None)
    filename = data.get("fileName", None)
    table = data.get("databaseName", None)
    select_condition = ""
    if time:
        select_condition = "time >= '" + str(time[0]) + "' AND time <= '" + str(time[1]) + "'"
    if user is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "user = '" + user + "'"
    if filename is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "filename = '" + filename + "'"
    if table != "" and table is not None:
        if select_condition != "":
            select_condition += " and "
        select_condition = select_condition + "tablename = '" + table + "'"
    if select_condition != "":
        select_condition = "WHERE " + select_condition + ";"
    query = f"""
        SELECT *
        FROM log {select_condition}
        """
    # print(query)
    cursor.execute(query)
    results = cursor.fetchall()
    # print(results)
    results_list = [list(result) for result in results]
    results_list = sorted(results_list, key=lambda x: (x[3] is None, x[3]), reverse=True)
    for result in results_list:
        result[0] = result[0].isoformat()
    total = len(results_list)
    return_dict = {"items": results_list[(page - 1) * limit:page * limit], "total": total}
    cursor.close()
    connection.close()
    print(return_dict)
    return json(return_dict)


###################演示用例####################
@app.route('/tianye_demo', methods=['POST', 'GET'])
async def transmit(request):
    if request.method == "POST":
        weight = request.json.get("weight", [1, 1, 1, 1])
    else:
        weight = [1, 1, 1, 1]  # 默认权重
    #file_path = r"data\田野案资金数据.xlsx"
    file_path = r"data\田野案资金数据.csv"
    df = run.run_pipeline(file_path)
    df = df.reset_index().rename(columns={"index": "name"})
    df = df.reset_index().rename(columns={
        'fused_score': 'fused_score',  # 嫌疑分值
        '命中标签数': 'count',  # 命中标签数
        '标签_快进快出': 'flag2',  # 快进快出
        '标签_高频交易': 'flag3',  # 高频交易
        '标签_时间集中': 'flag4',  # 时间集中
        '标签_小额测试': 'flag5'  # 小额测试
    })
    columns = ["flag2", "flag3", "flag4", "flag5"]

    if len(columns) != len(weight):
        return json({"error": "列名列表与权重长度不一致"}, status=400)
    df["rank"] = sum(df[col].astype(int) * w for col, w in zip(columns, weight))
    df = df.sort_values(by=["rank", "fused_score"], ascending=[False, False]).reset_index(drop=True)
    data = df.to_dict(orient="records")

    return json({"data": data})

from pathlib import Path
import re

@app.route("/get_all_TrackingNum",methods=["POST","GET"])
async def collect_excel_paths(request):
    excel_paths = []
    path_default = rf"D:\2025大创_地下田庄\server3.3\data\交易付款单和明细"
    # 遍历所有子文件夹中的 Excel 文件
    for path in Path(path_default).rglob("*.xlsx"):
        # 只保留文件名为纯数字的 Excel 表
        if re.fullmatch(r"\d+\.xlsx", path.name):
            excel_paths.append(str(path.resolve()))

    return json({"paths": excel_paths})

@app.route("/get_sus_TrackingNum",methods=["POST","GET"])
async def get_all_TrackingNum_filename(request):
    data = request.json
    path = data.get("filenames", [])
    result  = detectTrade(path)

    return json(result)



@app.route('/line_graph',methods=['POST','GET'])
async def load_line_graph_data(request):
    """
    :param request:包含用户名称（要查的账户）、时间选择：（按照月还是年）
    :return:json，其中”user:“账户名称、”timeSpan“：时间跨度【list】，（以对应时间选择作为基本单位） ”tradeAmount“[list]:对应的交易金额
    """
    #username = request.json.get("name")
    #time_class = request.json.get("timeUnit")
    #print(f"收到 name: {username}, timeUnit: {time_class}")
    #时间选择。是月还是年

    #table_name = data.get("table_name")

    #file_path = r"data\田野案资金数据.xlsx"
    {#connection = pymysql.connect(**connect_info)
    #cursor = connection.cursor()
    ###############################################
    #之后改成从数据库中
    # 编写查询语句
    #query = f"""
    #        SELECT 交易时间, 交易金额
    #        FROM {table_name}
    #        WHERE 交易户名 = %s;
    #    """

    ## 执行查询
    #cursor.execute(query, (username,))
    #results = cursor.fetchall()

    ## 关闭连接
    #cursor.close()
    #connection.close()
    ##################################################
    # 构造 DataFrame

    #df = pd.DataFrame(results, columns=["交易时间", "交易金额"])
    }
    #df_raw =  pd.read_excel(file_path, usecols=["交易户名", "交易时间", "交易金额"])
    #df = df_raw[df_raw['交易户名'] == username]

    #result = lineGraphDataGen.aggregate_transactions_by_gap(gap=time_class,data = df)

    #return json(result)
    try:
        data = request.json
        username = data.get("user")
        time_class = data.get("time_class")

        file_path = r"data\田野案资金数据.csv"
        df_raw = dd.read_csv(file_path,usecols=["交易户名","交易时间","交易金额"],
                             dtype = {"交易户名":str,"交易时间":str,"交易金额":"float64"},assume_missing=True)
        #df_raw = pd.read_excel(file_path, usecols=["交易户名", "交易时间", "交易金额"])

        # 修复 1：添加 .copy() 确保操作副本
        #df = df_raw.loc[df_raw['交易户名'] == username, :].copy()
        df = df_raw[df_raw["交易户名"] == username].compute()

        if df.head(1).shape[0] == 0:
            return json({"timeSpan": [], "tradeAmount": []})

        result = lineGraphDataGen.aggregate_transactions_by_gap(gap=time_class, data=df)
        return json(result)

    except Exception as e:
        traceback.print_exc()
        return json({"error": str(e)}, status=500)

@app.route('/drop_table', methods=['POST', 'GET'])
async def drop_a_table(request):
    try:
        data = request.json
        table4drop = data.get("table4drop", None)

        if not table4drop:
            return response.json({"error": "table4drop is required"}, status=400)

        drop_table(table4drop)  # 这里调用你的删除表函数

        return response.json({"isSuccess": 1}, status=200)

    except Exception as e:
        return response.json({"error": str(e)}, status=500)


# 启动服务
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000)
