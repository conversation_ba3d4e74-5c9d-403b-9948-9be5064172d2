{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749129426909}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzDQogICAgICB1cGxvYWRGaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsDQogICAgICB1cGxvYWRQcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDmlofku7bpgInmi6nnm7jlhbMNCiAgICAgIGF2YWlsYWJsZUZpbGVzOiBbXSwgLy8g5LuO5ZCO56uv5Yqo5oCB5Yqg6L29DQogICAgICBzZWxlY3RlZEZpbGVzOiBbXSwNCiAgICAgIGxvYWRpbmdGaWxlczogZmFsc2UsDQogICAgICBwcm9jZXNzaW5nOiBmYWxzZSwNCiAgICAgIHByb2Nlc3NQcm9ncmVzczogMCwNCiAgICAgIHByb2dyZXNzVGV4dDogJycsDQoNCiAgICAgIC8vIOW8guW4uOaVsOaNruWIl+ihqA0KICAgICAgZXhjZXB0aW9uTGlzdDogW10sIC8vIOS7juWQjuerr+W8guW4uOajgOa1i+iOt+WPlg0KICAgICAgc2Nyb2xsQ29udGFpbmVyOiBudWxsDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIC8vIOWIneWni+WMluaXtua4heepuuW8guW4uOaVsOaNruWIl+ihqO+8jOetieW+heeUqOaIt+mAieaLqeaWh+S7tg0KICAgIHRoaXMuZXhjZXB0aW9uTGlzdCA9IFtdDQogICAgLy8g5Yqg6L295Y+v55So5paH5Lu25YiX6KGoDQogICAgdGhpcy5sb2FkQXZhaWxhYmxlRmlsZXMoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5paH5Lu25LiK5Lyg55u45YWz5pa55rOVDQogICAgaGFuZGxlRmlsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IGZpbGVMaXN0DQogICAgICBjb25zb2xlLmxvZygn5LiK5Lyg5paH5Lu25YiX6KGo5pu05pawOicsIGZpbGVMaXN0KQ0KICAgIH0sDQoNCiAgICBoYW5kbGVGaWxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLnVwbG9hZEZpbGVMaXN0ID0gZmlsZUxpc3QNCiAgICAgIGNvbnNvbGUubG9nKCfmlofku7blt7Lnp7vpmaQ6JywgZmlsZS5uYW1lKQ0KICAgIH0sDQoNCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNFeGNlbCA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fA0KICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJw0KICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTANCg0KICAgICAgaWYgKCFpc0V4Y2VsKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oEV4Y2Vs5paH5Lu2IScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgaWYgKCFpc0x0MTBNKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzEwTUIhJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UgLy8g6Zi75q2i6Ieq5Yqo5LiK5Lyg77yM5omL5Yqo5o6n5Yi2DQogICAgfSwNCg0KICAgIGNsZWFyVXBsb2FkRmlsZXMoKSB7DQogICAgICB0aGlzLnVwbG9hZEZpbGVMaXN0ID0gW10NCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKQ0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXnqbrkuIrkvKDmlofku7bliJfooagnKQ0KICAgIH0sDQoNCiAgICBhc3luYyBoYW5kbGVVcGxvYWQoKSB7DQogICAgICBpZiAodGhpcy51cGxvYWRGaWxlTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHkuIrkvKDnmoRFeGNlbOaWh+S7ticpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLnVwbG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwDQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICflh4blpIfkuIrkvKDmlofku7YuLi4nDQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCg0KICAgICAgICAvLyDmt7vliqDmiYDmnInmlofku7bliLBGb3JtRGF0YQ0KICAgICAgICB0aGlzLnVwbG9hZEZpbGVMaXN0LmZvckVhY2goKGZpbGVJdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZXMnLCBmaWxlSXRlbS5yYXcpDQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g5qih5ouf6L+b5bqm5pu05pawDQogICAgICAgIGNvbnN0IHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMudXBsb2FkUHJvZ3Jlc3MgPCA5MCkgew0KICAgICAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTANCiAgICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gYOato+WcqOS4iuS8oOaWh+S7ti4uLiAke01hdGgucm91bmQodGhpcy51cGxvYWRQcm9ncmVzcyl9JWANCiAgICAgICAgICB9DQogICAgICAgIH0sIDIwMCkNCg0KICAgICAgICAvLyDov5nph4zlsIbmnaXov57mjqXlkI7nq69BUEnkuIrkvKDmlofku7YNCiAgICAgICAgLy8gY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvdXBsb2FkLWZpbGVzJywgZm9ybURhdGEsIHsNCiAgICAgICAgLy8gICBoZWFkZXJzOiB7DQogICAgICAgIC8vICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnDQogICAgICAgIC8vICAgfSwNCiAgICAgICAgLy8gICB0aW1lb3V0OiA2MDAwMA0KICAgICAgICAvLyB9KQ0KDQogICAgICAgIC8vIOaooeaLn+S4iuS8oOaXtumXtA0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMjAwMCkpDQoNCiAgICAgICAgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKQ0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMTAwDQogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gJ+aWh+S7tuS4iuS8oOWujOaIkO+8gScNCg0KICAgICAgICAvLyDmqKHmi5/kuIrkvKDmiJDlip/vvIzmt7vliqDliLDlj6/nlKjmlofku7bliJfooagNCiAgICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdC5mb3JFYWNoKChmaWxlSXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb25zdCBuZXdGaWxlID0gew0KICAgICAgICAgICAgaWQ6IHRoaXMuYXZhaWxhYmxlRmlsZXMubGVuZ3RoICsgaW5kZXggKyAxLA0KICAgICAgICAgICAgZmlsZU5hbWU6IGZpbGVJdGVtLm5hbWUsDQogICAgICAgICAgICB1cGxvYWREYXRlOiBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicpLA0KICAgICAgICAgICAgcmVjb3JkQ291bnQ6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUwMDApICsgMTAwLCAvLyDmqKHmi5/orrDlvZXmlbANCiAgICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5hdmFpbGFibGVGaWxlcy5wdXNoKG5ld0ZpbGUpDQogICAgICAgIH0pDQoNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/kuIrkvKAgJHt0aGlzLnVwbG9hZEZpbGVMaXN0Lmxlbmd0aH0g5Liq5paH5Lu2YCkNCiAgICAgICAgdGhpcy5jbGVhclVwbG9hZEZpbGVzKCkNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5LiK5Lyg5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMA0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICcnDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOS4iuS8oOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwDQogICAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAnJw0KICAgICAgICB9LCAzMDAwKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3lj6/nlKjmlofku7bliJfooagNCiAgICBhc3luYyBsb2FkQXZhaWxhYmxlRmlsZXMoKSB7DQogICAgICB0aGlzLmxvYWRpbmdGaWxlcyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiwg+eUqOWQjuerr0FQSeiOt+WPluaJgOaciUV4Y2Vs5paH5Lu26Lev5b6EDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2dldF9hbGxfVHJhY2tpbmdOdW0nKQ0KICAgICAgICBjb25zb2xlLmxvZygn5ZCO56uv6L+U5Zue55qE5paH5Lu26Lev5b6EOicsIHJlc3BvbnNlLmRhdGEpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5wYXRocykgew0KICAgICAgICAgIC8vIOWwhuaWh+S7tui3r+W+hOi9rOaNouS4uuaWh+S7tuWIl+ihqOagvOW8jw0KICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRmlsZXMgPSByZXNwb25zZS5kYXRhLnBhdGhzLm1hcCgocGF0aCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcGF0aC5zcGxpdCgnXFwnKS5wb3AoKSB8fCBwYXRoLnNwbGl0KCcvJykucG9wKCkgLy8g5o+Q5Y+W5paH5Lu25ZCNDQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBpZDogaW5kZXggKyAxLA0KICAgICAgICAgICAgICBmaWxlTmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgIGZpbGVQYXRoOiBwYXRoLCAvLyDkv53lrZjlrozmlbTot6/lvoTnlKjkuo7lkI7nq6/lpITnkIYNCiAgICAgICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMTItMjAgMTA6MDA6MDAnLCAvLyDlkI7nq6/msqHmnInmj5Dkvpvml7bpl7TvvIzkvb/nlKjpu5jorqTlgLwNCiAgICAgICAgICAgICAgcmVjb3JkQ291bnQ6IG51bGwsIC8vIOWQjuerr+ayoeacieaPkOS+m+iusOW9leaVsA0KICAgICAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOWKoOi9veS6hiAke3RoaXMuYXZhaWxhYmxlRmlsZXMubGVuZ3RofSDkuKrmlofku7ZgKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5om+5Yiw5Y+v55So55qERXhjZWzmlofku7YnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlofku7bliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaWh+S7tuWIl+ihqOWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmdGaWxlcyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaWh+S7tumAieaLqeWPmOWMlg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcyA9IHNlbGVjdGlvbg0KICAgICAgY29uc29sZS5sb2coJ+W3sumAieaLqeaWh+S7tjonLCBzZWxlY3Rpb24pDQogICAgfSwNCg0KICAgIC8vIOenu+mZpOW3sumAieaLqeeahOaWh+S7tg0KICAgIHJlbW92ZVNlbGVjdGVkRmlsZShmaWxlKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuc2VsZWN0ZWRGaWxlcy5maW5kSW5kZXgoZiA9PiBmLmlkID09PSBmaWxlLmlkKQ0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEZpbGVzLnNwbGljZShpbmRleCwgMSkNCiAgICAgIH0NCiAgICAgIC8vIOWQjOaXtuabtOaWsOihqOagvOmAieaLqeeKtuaAgQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMuZmlsZVRhYmxlDQogICAgICAgIGlmICh0YWJsZSkgew0KICAgICAgICAgIHRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihmaWxlLCBmYWxzZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5riF56m66YCJ5oupDQogICAgY2xlYXJTZWxlY3Rpb24oKSB7DQogICAgICB0aGlzLnNlbGVjdGVkRmlsZXMgPSBbXQ0KICAgICAgLy8g5riF56m66KGo5qC86YCJ5oupDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IHRhYmxlID0gdGhpcy4kcmVmcy5maWxlVGFibGUNCiAgICAgICAgaWYgKHRhYmxlKSB7DQogICAgICAgICAgdGFibGUuY2xlYXJTZWxlY3Rpb24oKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXnqbrmlofku7bpgInmi6knKQ0KICAgIH0sDQogICAgYXN5bmMgcHJvY2Vzc1NlbGVjdGVkRmlsZXMoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWkhOeQhueahOaWh+S7ticpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLnByb2Nlc3NpbmcgPSB0cnVlDQogICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyA9IDANCiAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+W8gOWni+WkhOeQhuaWh+S7ti4uLicNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5qih5ouf6L+b5bqm5pu05pawDQogICAgICAgIGNvbnN0IHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMucHJvY2Vzc1Byb2dyZXNzIDwgOTApIHsNCiAgICAgICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzICs9IE1hdGgucmFuZG9tKCkgKiAxNQ0KICAgICAgICAgICAgY29uc3QgY3VycmVudFN0ZXAgPSBNYXRoLmZsb29yKHRoaXMucHJvY2Vzc1Byb2dyZXNzIC8gMzApDQogICAgICAgICAgICBjb25zdCBzdGVwcyA9IFsn5q2j5Zyo6K+75Y+W5paH5Lu2Li4uJywgJ+ato+WcqOWQiOW5tuaVsOaNri4uLicsICfmraPlnKjliIbmnpDlvILluLguLi4nXQ0KICAgICAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSBzdGVwc1tjdXJyZW50U3RlcF0gfHwgJ+WkhOeQhuS4rS4uLicNCiAgICAgICAgICB9DQogICAgICAgIH0sIDMwMCkNCg0KICAgICAgICAvLyDosIPnlKjlkI7nq6/lvILluLjmo4DmtYvmjqXlj6MNCiAgICAgICAgY29uc3QgZmlsZVBhdGhzID0gdGhpcy5zZWxlY3RlZEZpbGVzLm1hcChmID0+IGYuZmlsZVBhdGgpDQogICAgICAgIGNvbnNvbGUubG9nKCflj5HpgIHliLDlkI7nq6/nmoTmlofku7bot6/lvoQ6JywgZmlsZVBhdGhzKQ0KDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+ato+WcqOiwg+eUqOWQjuerr+WIhuaekOaOpeWPoy4uLicNCg0KICAgICAgICAvLyDnnJ/mraPosIPnlKjlkI7nq69BUEkNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvZ2V0X3N1c19UcmFja2luZ051bScsIHsNCiAgICAgICAgICBmaWxlbmFtZXM6IGZpbGVQYXRocw0KICAgICAgICB9KQ0KDQogICAgICAgIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCkNCiAgICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAxMDANCiAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAn5pWw5o2u5aSE55CG5a6M5oiQ77yBJw0KDQogICAgICAgIGNvbnNvbGUubG9nKCflkI7nq6/ov5Tlm57nmoTlvILluLjmo4DmtYvnu5Pmnpw6JywgcmVzcG9uc2UuZGF0YSkNCg0KICAgICAgICAvLyDlpITnkIblkI7nq6/ov5Tlm57nmoTlvILluLjmlbDmja4NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBleGNlcHRpb25MaXN0ID0gW10NCg0KICAgICAgICAgIC8vIOmBjeWOhuWQjuerr+i/lOWbnueahOWQhOenjeW8guW4uOexu+Weiw0KICAgICAgICAgIE9iamVjdC5rZXlzKHJlc3BvbnNlLmRhdGEpLmZvckVhY2goZXhjZXB0aW9uVHlwZSA9PiB7DQogICAgICAgICAgICBjb25zdCBleGNlcHRpb25zID0gcmVzcG9uc2UuZGF0YVtleGNlcHRpb25UeXBlXQ0KICAgICAgICAgICAgaWYgKGV4Y2VwdGlvbnMgJiYgZXhjZXB0aW9ucy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIGV4Y2VwdGlvbnMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICAvLyDmoLnmja7lkI7nq6/ov5Tlm57nmoTmlbDmja7nu5PmnoTovazmjaLkuLrliY3nq6/mmL7npLrmoLzlvI8NCiAgICAgICAgICAgICAgICBjb25zdCBleGNlcHRpb24gPSB7DQogICAgICAgICAgICAgICAgICBvcmRlck5vOiBpdGVtWyforqLljZXlj7cnXSB8fCBg5byC5bi4LSR7RGF0ZS5ub3coKX0tJHtpbmRleH1gLA0KICAgICAgICAgICAgICAgICAgY2F0ZWdvcnk6IGV4Y2VwdGlvblR5cGUsIC8vIOW8guW4uOexu+Wei+S9nOS4uuWIhuexuw0KICAgICAgICAgICAgICAgICAgc3BlY3M6IGAke2V4Y2VwdGlvblR5cGV95byC5bi4YCwNCiAgICAgICAgICAgICAgICAgIHVuaXRQcmljZTogMCwNCiAgICAgICAgICAgICAgICAgIHF1YW50aXR5OiAxLA0KICAgICAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IDAsDQogICAgICAgICAgICAgICAgICBwYXllck5hbWU6IGl0ZW1bJ+aUr+S7mOS6uuWnk+WQjSddIHx8ICfmnKrnn6UnLA0KICAgICAgICAgICAgICAgICAgaWROdW1iZXI6IGl0ZW1bJ+aUr+S7mOS6uui6q+S7veivgeWPtyddIHx8ICfmnKrnn6UnLA0KICAgICAgICAgICAgICAgICAgcGhvbmU6ICfmnKrmj5DkvpsnLA0KICAgICAgICAgICAgICAgICAgb3JkZXJEYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSwNCiAgICAgICAgICAgICAgICAgIG9yZGVyVGltZTogbmV3IERhdGUoKS50b1RpbWVTdHJpbmcoKS5zcGxpdCgnICcpWzBdLA0KICAgICAgICAgICAgICAgICAgcGF5bWVudERhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLA0KICAgICAgICAgICAgICAgICAgcGF5bWVudFRpbWU6IG5ldyBEYXRlKCkudG9UaW1lU3RyaW5nKCkuc3BsaXQoJyAnKVswXSwNCiAgICAgICAgICAgICAgICAgIGxvZ2lzdGljc05vOiBpdGVtWyfnianmtYHljZXlj7cnXSB8fCAn5pyq55+lJywNCiAgICAgICAgICAgICAgICAgIGV4Y2VwdGlvblR5cGU6IGV4Y2VwdGlvblR5cGUgLy8g5re75Yqg5byC5bi457G75Z6L5a2X5q61DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGV4Y2VwdGlvbkxpc3QucHVzaChleGNlcHRpb24pDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCg0KICAgICAgICAgIHRoaXMuZXhjZXB0aW9uTGlzdCA9IGV4Y2VwdGlvbkxpc3QNCg0KICAgICAgICAgIGlmIChleGNlcHRpb25MaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5aSE55CGICR7dGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aH0g5Liq5paH5Lu277yM5Y+R546wICR7ZXhjZXB0aW9uTGlzdC5sZW5ndGh9IOadoeW8guW4uOaVsOaNrmApDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbyhg5oiQ5Yqf5aSE55CGICR7dGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aH0g5Liq5paH5Lu277yM5pyq5Y+R546w5byC5bi45pWw5o2uYCkNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflkI7nq6/ov5Tlm57mlbDmja7moLzlvI/lvILluLgnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCflpITnkIblpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMA0KICAgICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICcnDQoNCiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5aSE55CG5aSx6LSlOiAke2Vycm9yLnJlc3BvbnNlLnN0YXR1c30gLSAke2Vycm9yLnJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgICB9IGVsc2UgaWYgKGVycm9yLnJlcXVlc3QpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zov57mjqXlpLHotKXvvIzor7fmo4Dmn6XlkI7nq6/mnI3liqHmmK/lkKblkK/liqgnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOWkhOeQhuWksei0pTogJHtlcnJvci5tZXNzYWdlfWApDQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMucHJvY2Vzc2luZyA9IGZhbHNlDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMA0KICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgfSwgMzAwMCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlU2Nyb2xsKGV2ZW50KSB7DQogICAgICAvLyDlpITnkIbmu5rliqjkuovku7YNCiAgICAgIGNvbnNvbGUubG9nKCdTY3JvbGxpbmcuLi4nLCBldmVudCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到服务器</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择文件进行异常检测</h3>\r\n<p class=\"section-desc\">从已上传的文件中选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"200\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\" />\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"uploadDate\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount.toLocaleString() }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedFile(file)\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"exceptionType\" label=\"异常类型\" width=\"150\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"getExceptionTypeColor(row.exceptionType)\" size=\"small\">\r\n{{ row.exceptionType }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // 文件选择相关\r\n      availableFiles: [], // 从后端动态加载\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 这里将来连接后端API上传文件\r\n        // const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n        //   headers: {\r\n        //     'Content-Type': 'multipart/form-data'\r\n        //   },\r\n        //   timeout: 60000\r\n        // })\r\n\r\n        // 模拟上传时间\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 模拟上传成功，添加到可用文件列表\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          const newFile = {\r\n            id: this.availableFiles.length + index + 1,\r\n            fileName: fileItem.name,\r\n            uploadDate: new Date().toLocaleString('zh-CN'),\r\n            recordCount: Math.floor(Math.random() * 5000) + 100, // 模拟记录数\r\n            status: 'available'\r\n          }\r\n          this.availableFiles.push(newFile)\r\n        })\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的文件路径:', response.data)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为文件列表格式\r\n          this.availableFiles = response.data.paths.map((path, index) => {\r\n            const fileName = path.split('\\\\').pop() || path.split('/').pop() // 提取文件名\r\n            return {\r\n              id: index + 1,\r\n              fileName: fileName,\r\n              filePath: path, // 保存完整路径用于后端处理\r\n              uploadDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableFiles.length} 个文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        this.$message.error('加载文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理文件...'\r\n\r\n      try {\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedFiles.map(f => f.filePath)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 根据后端返回的数据结构转换为前端显示格式\r\n                const exception = {\r\n                  orderNo: item['订单号'] || `异常-${Date.now()}-${index}`,\r\n                  category: exceptionType, // 异常类型作为分类\r\n                  specs: `${exceptionType}异常`,\r\n                  unitPrice: 0,\r\n                  quantity: 1,\r\n                  totalAmount: 0,\r\n                  payerName: item['支付人姓名'] || '未知',\r\n                  idNumber: item['支付人身份证号'] || '未知',\r\n                  phone: '未提供',\r\n                  orderDate: new Date().toISOString().split('T')[0],\r\n                  orderTime: new Date().toTimeString().split(' ')[0],\r\n                  paymentDate: new Date().toISOString().split('T')[0],\r\n                  paymentTime: new Date().toTimeString().split(' ')[0],\r\n                  logisticsNo: item['物流单号'] || '未知',\r\n                  exceptionType: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedFiles.length} 个文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n  height:250px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  height:-10px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}