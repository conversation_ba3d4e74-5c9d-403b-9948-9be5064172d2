<template>
<div class="app-container">
<div class="filter-container">
<el-select
v-model="value"
placeholder="数据表"
style="width: 200px; margin-right: 15px;"
@change="handleSelectChange"
>
<el-option
v-for="item in options"
:key="item.value"
:label="item.label"
:value="item.value"
/>
</el-select>

<el-date-picker
v-model="dateRange"
type="datetimerange"
range-separator="至"
start-placeholder="起始日期"
end-placeholder="结束日期"
style="width: 380px; margin-right: 15px;"
/>

<el-button
type="primary"
icon="el-icon-search"
@click="handleSearch"
>
查询
</el-button>
</div>

<el-card class="box-card">
<div slot="header" class="clearfix">
<span>异常物流订单列表</span>
</div>
<div class="scroll-container">
<div ref="scrollContainer" class="custom-scrollbar" @scroll="handleScroll">
<el-table
:data="exceptionList"
border
fit
highlight-current-row
style="width: 100%; height: 100%"
>
<el-table-column prop="orderNo" label="订单号" width="180" align="center" />
<el-table-column prop="category" label="商品品类" width="120" />
<el-table-column prop="specs" label="商品规格" width="180" />
<el-table-column prop="unitPrice" label="单价" align="right" width="110">
<template #default="{row}">
¥{{ row.unitPrice.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="quantity" label="数量" width="80" align="center" />
<el-table-column prop="totalAmount" label="订单金额" align="right" width="130">
<template #default="{row}">
¥{{ row.totalAmount.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="payerName" label="支付人" width="120" />
<el-table-column prop="idNumber" label="身份证号" width="180" />
<el-table-column prop="phone" label="联系电话" width="130" />
<el-table-column prop="orderDate" label="下单日期" width="120" />
<el-table-column prop="orderTime" label="下单时间" width="100" />
<el-table-column prop="paymentDate" label="支付日期" width="120" />
<el-table-column prop="paymentTime" label="支付时间" width="100" />
<el-table-column prop="logisticsNo" label="物流单号" width="180" />
</el-table>
</div>
</div>
</el-card>
</div>

</template>

<script>
import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      value: '',
      dateRange: [],
      options: [],
      exceptionList: [
        {
          orderNo: 'DD20240715001',
          category: '电子产品',
          specs: '笔记本电脑/16GB 512GB',
          unitPrice: 8999.00,
          quantity: 1,
          totalAmount: 8999.00,
          payerName: '李四',
          idNumber: '310***********5678',
          phone: '13900139000',
          orderDate: '2024-07-15',
          orderTime: '10:15',
          paymentDate: '2024-07-15',
          paymentTime: '10:20',
          logisticsNo: 'WL987654321'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        }
      ],
      scrollContainer: null
    }
  },
  mounted() {
    this.loadTableOptions()
  },
  methods: {
    handleSelectChange(value) {
      console.log('选中的数据表:', value)
    },
    handleSearch() {
      // 查询逻辑
    },
    async loadTableOptions() {
      try {
        const response = await axios.get('http://127.0.0.1:8000/all_tables')
        this.options = response.data.all_tables.map(item => ({
          label: item,
          value: item
        }))
      } catch (error) {
        console.error('加载数据表失败:', error)
      }
    },
    handleScroll(event) {
      // 处理滚动事件，如果需要的话
      console.log('Scrolling...', event)
    }
  }
}
</script>

<style scoped>
.app-container {
padding: 20px;
}
.filter-container {
margin-bottom: 20px;
display: flex;
align-items: center;
}
.box-card {
margin-top: 20px;
}
.el-table {
margin-top: 15px;
}
.scroll-container {
  height: 600px;
  position: relative;
}

.custom-scrollbar {
  height: 100%;
  overflow: auto;
  padding-right: 12px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

</style>
