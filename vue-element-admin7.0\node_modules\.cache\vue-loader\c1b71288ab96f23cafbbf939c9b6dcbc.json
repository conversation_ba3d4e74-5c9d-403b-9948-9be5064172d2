{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=template&id=09ac478a&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749130231676}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1731739006000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}