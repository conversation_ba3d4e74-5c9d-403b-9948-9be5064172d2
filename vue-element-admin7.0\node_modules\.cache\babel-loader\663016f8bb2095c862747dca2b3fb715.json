{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749134096612}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "name", "data", "uploadFileList", "uploading", "uploadProgress", "uploadProgressText", "availableTables", "selectedTables", "loadingFiles", "processing", "processProgress", "progressText", "exceptionList", "exceptionColumns", "scrollContainer", "mounted", "loadAvailableFiles", "methods", "handleFileChange", "file", "fileList", "console", "log", "handleFileRemove", "beforeUpload", "isExcel", "type", "isLt10M", "size", "$message", "error", "clearUploadFiles", "$refs", "upload", "clearFiles", "info", "handleUpload", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "formData", "progressInterval", "response", "_response$data", "wrap", "_callee$", "_context", "prev", "next", "length", "warning", "abrupt", "FormData", "for<PERSON>ach", "fileItem", "index", "append", "raw", "setInterval", "Math", "random", "concat", "round", "post", "headers", "timeout", "sent", "success", "Error", "message", "t0", "status", "warn", "Promise", "resolve", "setTimeout", "clearInterval", "t1", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "paths", "map", "filePath", "fileName", "split", "pop", "tableName", "replace", "id", "createDate", "recordCount", "handleSelectionChange", "selection", "removeSelectedTable", "table", "_this3", "findIndex", "t", "splice", "$nextTick", "tableRef", "tableList", "toggleRowSelection", "clearSelection", "_this4", "processSelectedTables", "_this5", "_callee3", "filePaths", "_error$response$data", "_callee3$", "_context3", "currentStep", "floor", "steps", "filenames", "Object", "keys", "exceptionType", "exceptions", "item", "exception", "_objectSpread", "异常类型", "push", "prop", "label", "width", "align", "request", "handleScroll", "event", "getExceptionTypeColor", "colorMap", "getColumnWidth", "columnName", "widthMap", "getColumnAlign", "alignMap"], "sources": ["src/components/Charts/OrderException.vue"], "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column\r\nv-for=\"column in exceptionColumns\"\r\n:key=\"column.prop\"\r\n:prop=\"column.prop\"\r\n:label=\"column.label\"\r\n:width=\"column.width\"\r\n:align=\"column.align\"\r\n>\r\n<template #default=\"{row}\">\r\n<el-tag\r\nv-if=\"column.type === 'tag'\"\r\n:type=\"getExceptionTypeColor(row[column.prop])\"\r\nsize=\"small\"\r\n>\r\n{{ row[column.prop] }}\r\n</el-tag>\r\n<span v-else>{{ row[column.prop] || '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          console.log('后端返回的原始数据结构:', response.data)\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            console.log(`异常类型 ${exceptionType} 的数据:`, exceptions)\r\n\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  异常类型: exceptionType, // 添加异常类型字段\r\n                  ...item // 展开后端返回的所有字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 根据detectTrade函数的返回结构，固定列配置\r\n          this.exceptionColumns = [\r\n            {\r\n              prop: '异常类型',\r\n              label: '异常类型',\r\n              width: 150,\r\n              align: 'center',\r\n              type: 'tag'\r\n            },\r\n            {\r\n              prop: '订单号',\r\n              label: '订单号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人姓名',\r\n              label: '支付人姓名',\r\n              width: 120,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人身份证号',\r\n              label: '支付人身份证号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '物流单号',\r\n              label: '物流单号',\r\n              width: 180,\r\n              align: 'center'\r\n            }\r\n          ]\r\n\r\n          this.exceptionList = exceptionList\r\n          console.log('处理后的异常数据列表:', this.exceptionList)\r\n          console.log('表格列配置:', this.exceptionColumns)\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA;MACAC,SAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,eAAA;MAAA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,aAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAH,aAAA;IACA;IACA,KAAAI,kBAAA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,QAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAAJ,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,WAAAH,IAAA,CAAAnB,IAAA;IACA;IAEAwB,YAAA,WAAAA,aAAAL,IAAA;MACA,IAAAM,OAAA,GAAAN,IAAA,CAAAO,IAAA,4EACAP,IAAA,CAAAO,IAAA;MACA,IAAAC,OAAA,GAAAR,IAAA,CAAAS,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAH,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA;MACA,KAAA7B,cAAA;MACA,KAAA8B,KAAA,CAAAC,MAAA,CAAAC,UAAA;MACA,KAAAL,QAAA,CAAAM,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,gBAAA,EAAAC,QAAA,EAAAC,cAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAb,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;gBAAAH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAb,KAAA,CAAAR,QAAA,CAAAuB,OAAA;cAAA,OAAAJ,QAAA,CAAAK,MAAA;YAAA;cAIAhB,KAAA,CAAAlC,SAAA;cACAkC,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cAAA2C,QAAA,CAAAC,IAAA;cAGAP,QAAA,OAAAY,QAAA,IAEA;cACAjB,KAAA,CAAAnC,cAAA,CAAAqD,OAAA,WAAAC,QAAA,EAAAC,KAAA;gBACAf,QAAA,CAAAgB,MAAA,UAAAF,QAAA,CAAAG,GAAA;cACA;;cAEA;cACAhB,gBAAA,GAAAiB,WAAA;gBACA,IAAAvB,KAAA,CAAAjC,cAAA;kBACAiC,KAAA,CAAAjC,cAAA,IAAAyD,IAAA,CAAAC,MAAA;kBACAzB,KAAA,CAAAhC,kBAAA,8CAAA0D,MAAA,CAAAF,IAAA,CAAAG,KAAA,CAAA3B,KAAA,CAAAjC,cAAA;gBACA;cACA,SAEA;cACA;cAAA4C,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAnD,KAAA,CAAAkE,IAAA,uCAAAvB,QAAA;gBACAwB,OAAA;kBACA;gBACA;gBACAC,OAAA;cACA;YAAA;cALAvB,QAAA,GAAAI,QAAA,CAAAoB,IAAA;cAAA,MAQA,CAAAxB,QAAA,CAAA3C,IAAA,KAAA2C,QAAA,CAAA3C,IAAA,CAAAoE,OAAA;gBAAArB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoB,KAAA,GAAAzB,cAAA,GAAAD,QAAA,CAAA3C,IAAA,cAAA4C,cAAA,uBAAAA,cAAA,CAAA0B,OAAA;YAAA;cAAAvB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAwB,EAAA,GAAAxB,QAAA;cAAA,MAIAA,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,IAAAI,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,CAAA6B,MAAA;gBAAAzB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAqD,IAAA;cAAA1B,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAyB,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAAA5B,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,MAAAF,QAAA,CAAAwB,EAAA;YAAA;cAMAM,aAAA,CAAAnC,gBAAA;cACAN,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;;cAEA;cAAA2C,QAAA,CAAAE,IAAA;cAAA,OACAb,KAAA,CAAArB,kBAAA;YAAA;cAEAqB,KAAA,CAAAR,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAA1B,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;cACAd,KAAA,CAAAN,gBAAA;cAAAiB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAA+B,EAAA,GAAA/B,QAAA;cAEA3B,OAAA,CAAAS,KAAA,UAAAkB,QAAA,CAAA+B,EAAA;cACA1C,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cACAgC,KAAA,CAAAR,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAf,QAAA,CAAA+B,EAAA,CAAAR,OAAA;YAAA;cAAAvB,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAlC,SAAA;cACA0E,UAAA;gBACAxC,KAAA,CAAAjC,cAAA;gBACAiC,KAAA,CAAAhC,kBAAA;cACA;cAAA,OAAA2C,QAAA,CAAAgC,MAAA;YAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAiC,IAAA;UAAA;QAAA,GAAAxC,OAAA;MAAA;IAEA;IAEA;IACAzB,kBAAA,WAAAA,mBAAA;MAAA,IAAAkE,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAvC,QAAA;QAAA,OAAAL,mBAAA,GAAAO,IAAA,UAAAsC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;YAAA;cACAgC,MAAA,CAAA1E,YAAA;cAAA6E,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAnC,IAAA;cAAA,OAGAnD,KAAA,CAAAkE,IAAA;YAAA;cAAArB,QAAA,GAAAyC,SAAA,CAAAjB,IAAA;cACA/C,OAAA,CAAAC,GAAA,oBAAAsB,QAAA,CAAA3C,IAAA;cACAoB,OAAA,CAAAC,GAAA,2BAAAsB,QAAA,CAAA3C,IAAA,CAAAqF,KAAA;cAEA,IAAA1C,QAAA,CAAA3C,IAAA,IAAA2C,QAAA,CAAA3C,IAAA,CAAAqF,KAAA;gBACA;gBACAJ,MAAA,CAAA5E,eAAA,GAAAsC,QAAA,CAAA3C,IAAA,CAAAqF,KAAA,CAAAC,GAAA,WAAAC,QAAA,EAAA/B,KAAA;kBACA;kBACA,IAAAgC,QAAA,GAAAD,QAAA,CAAAE,KAAA,OAAAC,GAAA,MAAAH,QAAA,CAAAE,KAAA,MAAAC,GAAA;kBACA,IAAAC,SAAA,GAAAH,QAAA,CAAAI,OAAA;;kBAEA;oBACAC,EAAA,EAAArC,KAAA;oBACAmC,SAAA,EAAAA,SAAA;oBAAA;oBACAJ,QAAA,EAAAA,QAAA;oBAAA;oBACAO,UAAA;oBAAA;oBACAC,WAAA;oBAAA;oBACAvB,MAAA;kBACA;gBACA;gBACAS,MAAA,CAAArD,QAAA,CAAAwC,OAAA,uBAAAN,MAAA,CAAAmB,MAAA,CAAA5E,eAAA,CAAA6C,MAAA;cACA;gBACA+B,MAAA,CAAArD,QAAA,CAAAuB,OAAA;cACA;cAAAiC,SAAA,CAAAnC,IAAA;cAAA;YAAA;cAAAmC,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAEAhE,OAAA,CAAAS,KAAA,mBAAAuD,SAAA,CAAAb,EAAA;cACAU,MAAA,CAAArD,QAAA,CAAAC,KAAA,qBAAAuD,SAAA,CAAAb,EAAA,CAAAD,OAAA;YAAA;cAAAc,SAAA,CAAApC,IAAA;cAEAiC,MAAA,CAAA1E,YAAA;cAAA,OAAA6E,SAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAc,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3F,cAAA,GAAA2F,SAAA;MACA7E,OAAA,CAAAC,GAAA,gBAAA4E,SAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAA5C,KAAA,QAAAlD,cAAA,CAAA+F,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAT,EAAA,KAAAM,KAAA,CAAAN,EAAA;MAAA;MACA,IAAArC,KAAA;QACA,KAAAlD,cAAA,CAAAiG,MAAA,CAAA/C,KAAA;MACA;MACA;MACA,KAAAgD,SAAA;QACA,IAAAC,QAAA,GAAAL,MAAA,CAAArE,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAE,kBAAA,CAAAR,KAAA;QACA;MACA;IACA;IAEA;IACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAvG,cAAA;MACA;MACA,KAAAkG,SAAA;QACA,IAAAC,QAAA,GAAAI,MAAA,CAAA9E,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAG,cAAA;QACA;MACA;MACA,KAAAhF,QAAA,CAAAM,IAAA;IACA;IACA4E,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA1E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyE,SAAA;QAAA,IAAAtE,gBAAA,EAAAuE,SAAA,EAAAtE,QAAA,EAAAhC,aAAA,EAAAuG,oBAAA;QAAA,OAAA5E,mBAAA,GAAAO,IAAA,UAAAsE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApE,IAAA,GAAAoE,SAAA,CAAAnE,IAAA;YAAA;cAAA,MACA8D,MAAA,CAAAzG,cAAA,CAAA4C,MAAA;gBAAAkE,SAAA,CAAAnE,IAAA;gBAAA;cAAA;cACA8D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cAAA,OAAAiE,SAAA,CAAAhE,MAAA;YAAA;cAIA2D,MAAA,CAAAvG,UAAA;cACAuG,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAAA0G,SAAA,CAAApE,IAAA;cAGA;cACAN,gBAAA,GAAAiB,WAAA;gBACA,IAAAoD,MAAA,CAAAtG,eAAA;kBACAsG,MAAA,CAAAtG,eAAA,IAAAmD,IAAA,CAAAC,MAAA;kBACA,IAAAwD,WAAA,GAAAzD,IAAA,CAAA0D,KAAA,CAAAP,MAAA,CAAAtG,eAAA;kBACA,IAAA8G,KAAA;kBACAR,MAAA,CAAArG,YAAA,GAAA6G,KAAA,CAAAF,WAAA;gBACA;cACA,SAEA;cACAJ,SAAA,GAAAF,MAAA,CAAAzG,cAAA,CAAAgF,GAAA,WAAAgB,CAAA;gBAAA,OAAAA,CAAA,CAAAf,QAAA;cAAA;cACAnE,OAAA,CAAAC,GAAA,aAAA0F,MAAA,CAAAzG,cAAA;cACAc,OAAA,CAAAC,GAAA,gBAAA4F,SAAA;cACA7F,OAAA,CAAAC,GAAA,2BAAA4F,SAAA;cAEAF,MAAA,CAAArG,YAAA;;cAEA;cAAA0G,SAAA,CAAAnE,IAAA;cAAA,OACAnD,KAAA,CAAAkE,IAAA;gBACAwD,SAAA,EAAAP;cACA;YAAA;cAFAtE,QAAA,GAAAyE,SAAA,CAAAjD,IAAA;cAIAU,aAAA,CAAAnC,gBAAA;cACAqE,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAEAU,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA3C,IAAA;;cAEA;cACA,IAAA2C,QAAA,CAAA3C,IAAA;gBACAW,aAAA;gBAEAS,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA3C,IAAA;;gBAEA;gBACAyH,MAAA,CAAAC,IAAA,CAAA/E,QAAA,CAAA3C,IAAA,EAAAsD,OAAA,WAAAqE,aAAA;kBACA,IAAAC,UAAA,GAAAjF,QAAA,CAAA3C,IAAA,CAAA2H,aAAA;kBACAvG,OAAA,CAAAC,GAAA,6BAAAyC,MAAA,CAAA6D,aAAA,2BAAAC,UAAA;kBAEA,IAAAA,UAAA,IAAAA,UAAA,CAAA1E,MAAA;oBACA0E,UAAA,CAAAtE,OAAA,WAAAuE,IAAA,EAAArE,KAAA;sBACA;sBACA,IAAAsE,SAAA,GAAAC,aAAA;wBACAC,IAAA,EAAAL;sBAAA,GACAE,IAAA,CACA;sBACAlH,aAAA,CAAAsH,IAAA,CAAAH,SAAA;oBACA;kBACA;gBACA;;gBAEA;gBACAf,MAAA,CAAAnG,gBAAA,IACA;kBACAsH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;kBACA5G,IAAA;gBACA,GACA;kBACAyG,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,EACA;gBAEAtB,MAAA,CAAApG,aAAA,GAAAA,aAAA;gBACAS,OAAA,CAAAC,GAAA,gBAAA0F,MAAA,CAAApG,aAAA;gBACAS,OAAA,CAAAC,GAAA,WAAA0F,MAAA,CAAAnG,gBAAA;gBAEA,IAAAD,aAAA,CAAAuC,MAAA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAAiD,MAAA,CAAAzG,cAAA,CAAA4C,MAAA,iDAAAY,MAAA,CAAAnD,aAAA,CAAAuC,MAAA;gBACA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAM,IAAA,6BAAA4B,MAAA,CAAAiD,MAAA,CAAAzG,cAAA,CAAA4C,MAAA;gBACA;cACA;gBACA6D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cACA;cAAAiE,SAAA,CAAAnE,IAAA;cAAA;YAAA;cAAAmE,SAAA,CAAApE,IAAA;cAAAoE,SAAA,CAAA7C,EAAA,GAAA6C,SAAA;cAEAhG,OAAA,CAAAS,KAAA,UAAAuF,SAAA,CAAA7C,EAAA;cACAwC,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAEA,IAAA0G,SAAA,CAAA7C,EAAA,CAAA5B,QAAA;gBACAoE,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAsD,SAAA,CAAA7C,EAAA,CAAA5B,QAAA,CAAA6B,MAAA,SAAAV,MAAA,GAAAoD,oBAAA,GAAAE,SAAA,CAAA7C,EAAA,CAAA5B,QAAA,CAAA3C,IAAA,cAAAkH,oBAAA,uBAAAA,oBAAA,CAAA5C,OAAA,KAAA8C,SAAA,CAAA7C,EAAA,CAAAD,OAAA;cACA,WAAA8C,SAAA,CAAA7C,EAAA,CAAA+D,OAAA;gBACAvB,MAAA,CAAAnF,QAAA,CAAAC,KAAA;cACA;gBACAkF,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAsD,SAAA,CAAA7C,EAAA,CAAAD,OAAA;cACA;YAAA;cAAA8C,SAAA,CAAApE,IAAA;cAEA+D,MAAA,CAAAvG,UAAA;cACAoE,UAAA;gBACAmC,MAAA,CAAAtG,eAAA;gBACAsG,MAAA,CAAArG,YAAA;cACA;cAAA,OAAA0G,SAAA,CAAArC,MAAA;YAAA;YAAA;cAAA,OAAAqC,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IAEAuB,YAAA,WAAAA,aAAAC,KAAA;MACA;MACApH,OAAA,CAAAC,GAAA,iBAAAmH,KAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAd,aAAA;MACA,IAAAe,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAf,aAAA;IACA;IAEA;IACAgB,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,UAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAF,UAAA;MACA,IAAAG,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAH,UAAA;IACA;EACA;AACA", "ignoreList": []}]}