{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749133725761}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMjAyNVx1NTkyN1x1NTIxQl9cdTU3MzBcdTRFMEJcdTc1MzBcdTVFODQvdnVlLWVsZW1lbnQtYWRtaW43LjAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgX3JlZ2VuZXJhdG9yUnVudGltZSBmcm9tICJEOi8yMDI1XHU1OTI3XHU1MjFCX1x1NTczMFx1NEUwQlx1NzUzMFx1NUU4NC92dWUtZWxlbWVudC1hZG1pbjcuMC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzIwMjVcdTU5MjdcdTUyMUJfXHU1NzMwXHU0RTBCXHU3NTMwXHU1RTg0L3Z1ZS1lbGVtZW50LWFkbWluNy4wL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC1pbmRleC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzCiAgICAgIHVwbG9hZEZpbGVMaXN0OiBbXSwKICAgICAgdXBsb2FkaW5nOiBmYWxzZSwKICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsCiAgICAgIHVwbG9hZFByb2dyZXNzVGV4dDogJycsCiAgICAgIC8vIEV4Y2Vs5paH5Lu26YCJ5oup55u45YWzCiAgICAgIGF2YWlsYWJsZVRhYmxlczogW10sCiAgICAgIC8vIOS7juWQjuerr+WKqOaAgeWKoOi9vQogICAgICBzZWxlY3RlZFRhYmxlczogW10sCiAgICAgIGxvYWRpbmdGaWxlczogZmFsc2UsCiAgICAgIHByb2Nlc3Npbmc6IGZhbHNlLAogICAgICBwcm9jZXNzUHJvZ3Jlc3M6IDAsCiAgICAgIHByb2dyZXNzVGV4dDogJycsCiAgICAgIC8vIOW8guW4uOaVsOaNruWIl+ihqAogICAgICBleGNlcHRpb25MaXN0OiBbXSwKICAgICAgLy8g5LuO5ZCO56uv5byC5bi45qOA5rWL6I635Y+WCiAgICAgIGV4Y2VwdGlvbkNvbHVtbnM6IFtdLAogICAgICAvLyDliqjmgIHnlJ/miJDnmoTooajmoLzliJcKICAgICAgc2Nyb2xsQ29udGFpbmVyOiBudWxsCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8vIOWIneWni+WMluaXtua4heepuuW8guW4uOaVsOaNruWIl+ihqO+8jOetieW+heeUqOaIt+mAieaLqeaWh+S7tgogICAgdGhpcy5leGNlcHRpb25MaXN0ID0gW107CiAgICAvLyDliqDovb3lj6/nlKjmlofku7bliJfooagKICAgIHRoaXMubG9hZEF2YWlsYWJsZUZpbGVzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmlofku7bkuIrkvKDnm7jlhbPmlrnms5UKICAgIGhhbmRsZUZpbGVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUZpbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IGZpbGVMaXN0OwogICAgICBjb25zb2xlLmxvZygn5LiK5Lyg5paH5Lu25YiX6KGo5pu05pawOicsIGZpbGVMaXN0KTsKICAgIH0sCiAgICBoYW5kbGVGaWxlUmVtb3ZlOiBmdW5jdGlvbiBoYW5kbGVGaWxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QgPSBmaWxlTGlzdDsKICAgICAgY29uc29sZS5sb2coJ+aWh+S7tuW3suenu+mZpDonLCBmaWxlLm5hbWUpOwogICAgfSwKICAgIGJlZm9yZVVwbG9hZDogZnVuY3Rpb24gYmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgdmFyIGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJzsKICAgICAgdmFyIGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOwogICAgICBpZiAoIWlzRXhjZWwpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HMTBNQiEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIGZhbHNlOyAvLyDpmLvmraLoh6rliqjkuIrkvKDvvIzmiYvliqjmjqfliLYKICAgIH0sCiAgICBjbGVhclVwbG9hZEZpbGVzOiBmdW5jdGlvbiBjbGVhclVwbG9hZEZpbGVzKCkgewogICAgICB0aGlzLnVwbG9hZEZpbGVMaXN0ID0gW107CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXnqbrkuIrkvKDmlofku7bliJfooagnKTsKICAgIH0sCiAgICBoYW5kbGVVcGxvYWQ6IGZ1bmN0aW9uIGhhbmRsZVVwbG9hZCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBmb3JtRGF0YSwgcHJvZ3Jlc3NJbnRlcnZhbCwgcmVzcG9uc2UsIF9yZXNwb25zZSRkYXRhOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGlmICghKF90aGlzLnVwbG9hZEZpbGVMaXN0Lmxlbmd0aCA9PT0gMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeS4iuS8oOeahEV4Y2Vs5paH5Lu2Jyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfdGhpcy51cGxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF90aGlzLnVwbG9hZFByb2dyZXNzID0gMDsKICAgICAgICAgICAgICBfdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAn5YeG5aSH5LiK5Lyg5paH5Lu2Li4uJzsKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gNjsKICAgICAgICAgICAgICBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOyAvLyDmt7vliqDmiYDmnInmlofku7bliLBGb3JtRGF0YQogICAgICAgICAgICAgIF90aGlzLnVwbG9hZEZpbGVMaXN0LmZvckVhY2goZnVuY3Rpb24gKGZpbGVJdGVtLCBpbmRleCkgewogICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlcycsIGZpbGVJdGVtLnJhdyk7CiAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsAogICAgICAgICAgICAgIHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMudXBsb2FkUHJvZ3Jlc3MgPCA5MCkgewogICAgICAgICAgICAgICAgICBfdGhpcy51cGxvYWRQcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTA7CiAgICAgICAgICAgICAgICAgIF90aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICJcdTZCNjNcdTU3MjhcdTRFMEFcdTRGMjBcdTY1ODdcdTRFRjYuLi4gIi5jb25jYXQoTWF0aC5yb3VuZChfdGhpcy51cGxvYWRQcm9ncmVzcyksICIlIik7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSwgMjAwKTsgLy8g55yf5q2j6LCD55So5ZCO56uvQVBJ5LiK5Lyg5paH5Lu2CiAgICAgICAgICAgICAgLy8g5rOo5oSP77ya5aaC5p6c5ZCO56uv5rKh5pyJ5a6e546wIC91cGxvYWQtZmlsZXMg5o6l5Y+j77yM6K+35rOo6YeK5o6J5LiL6Z2i55qE5Luj56CB77yM5L2/55So5qih5ouf5LiK5LygCiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDEwOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMzsKICAgICAgICAgICAgICByZXR1cm4gYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL3VwbG9hZC1maWxlcycsIGZvcm1EYXRhLCB7CiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB0aW1lb3V0OiA2MDAwMAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICBpZiAoISghcmVzcG9uc2UuZGF0YSB8fCAhcmVzcG9uc2UuZGF0YS5zdWNjZXNzKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDE2OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigoKF9yZXNwb25zZSRkYXRhID0gcmVzcG9uc2UuZGF0YSkgPT09IG51bGwgfHwgX3Jlc3BvbnNlJGRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9yZXNwb25zZSRkYXRhLm1lc3NhZ2UpIHx8ICfkuIrkvKDlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSAxNjoKICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjc7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTg6CiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDE4OwogICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oMTApOwogICAgICAgICAgICAgIGlmICghKF9jb250ZXh0LnQwLnJlc3BvbnNlICYmIF9jb250ZXh0LnQwLnJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0KSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI2OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign5LiK5Lyg5o6l5Y+j5LiN5a2Y5Zyo77yM5L2/55So5qih5ouf5LiK5LygJyk7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI0OwogICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICAgICAgICAgICAgcmV0dXJuIHNldFRpbWVvdXQocmVzb2x2ZSwgMjAwMCk7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjQ6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI3OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDI2OgogICAgICAgICAgICAgIHRocm93IF9jb250ZXh0LnQwOwogICAgICAgICAgICBjYXNlIDI3OgogICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCk7CiAgICAgICAgICAgICAgX3RoaXMudXBsb2FkUHJvZ3Jlc3MgPSAxMDA7CiAgICAgICAgICAgICAgX3RoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gJ+aWh+S7tuS4iuS8oOWujOaIkO+8gSc7CgogICAgICAgICAgICAgIC8vIOS4iuS8oOaIkOWKn+WQju+8jOmHjeaWsOWKoOi9veacjeWKoeWZqOS4iueahEV4Y2Vs5paH5Lu25YiX6KGoCiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDMyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpcy5sb2FkQXZhaWxhYmxlRmlsZXMoKTsKICAgICAgICAgICAgY2FzZSAzMjoKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCJcdTYyMTBcdTUyOUZcdTRFMEFcdTRGMjAgIi5jb25jYXQoX3RoaXMudXBsb2FkRmlsZUxpc3QubGVuZ3RoLCAiIFx1NEUyQVx1NjU4N1x1NEVGNiIpKTsKICAgICAgICAgICAgICBfdGhpcy5jbGVhclVwbG9hZEZpbGVzKCk7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDQyOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM2OgogICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAzNjsKICAgICAgICAgICAgICBfY29udGV4dC50MSA9IF9jb250ZXh0WyJjYXRjaCJdKDYpOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4iuS8oOWksei0pTonLCBfY29udGV4dC50MSk7CiAgICAgICAgICAgICAgX3RoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwOwogICAgICAgICAgICAgIF90aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICcnOwogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKCJcdTRFMEFcdTRGMjBcdTU5MzFcdThEMjU6ICIuY29uY2F0KF9jb250ZXh0LnQxLm1lc3NhZ2UpKTsKICAgICAgICAgICAgY2FzZSA0MjoKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gNDI7CiAgICAgICAgICAgICAgX3RoaXMudXBsb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBfdGhpcy51cGxvYWRQcm9ncmVzcyA9IDA7CiAgICAgICAgICAgICAgICBfdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAnJzsKICAgICAgICAgICAgICB9LCAzMDAwKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuZmluaXNoKDQyKTsKICAgICAgICAgICAgY2FzZSA0NjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbNiwgMzYsIDQyLCA0Nl0sIFsxMCwgMThdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOWKoOi9veWPr+eUqOaVsOaNruihqOWIl+ihqAogICAgbG9hZEF2YWlsYWJsZUZpbGVzOiBmdW5jdGlvbiBsb2FkQXZhaWxhYmxlRmlsZXMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmdGaWxlcyA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAxOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2dldF9hbGxfVHJhY2tpbmdOdW0nKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+WQjuerr+i/lOWbnueahEV4Y2Vs5paH5Lu26Lev5b6EOicsIHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfov5nkupvmlofku7bmnaXoh6pwYXRoX2RlZmF1bHTmlofku7blpLk6JywgcmVzcG9uc2UuZGF0YS5wYXRocyk7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5wYXRocykgewogICAgICAgICAgICAgICAgLy8g5bCG5paH5Lu26Lev5b6E6L2s5o2i5Li65YmN56uv5pi+56S65qC85byPCiAgICAgICAgICAgICAgICBfdGhpczIuYXZhaWxhYmxlVGFibGVzID0gcmVzcG9uc2UuZGF0YS5wYXRocy5tYXAoZnVuY3Rpb24gKGZpbGVQYXRoLCBpbmRleCkgewogICAgICAgICAgICAgICAgICAvLyDmj5Dlj5bmlofku7blkI3kvZzkuLrooajlkI3mmL7npLoKICAgICAgICAgICAgICAgICAgdmFyIGZpbGVOYW1lID0gZmlsZVBhdGguc3BsaXQoJ1xcJykucG9wKCkgfHwgZmlsZVBhdGguc3BsaXQoJy8nKS5wb3AoKTsKICAgICAgICAgICAgICAgICAgdmFyIHRhYmxlTmFtZSA9IGZpbGVOYW1lLnJlcGxhY2UoJy54bHN4JywgJycpOyAvLyDnp7vpmaTmianlsZXlkI0KCiAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IGluZGV4ICsgMSwKICAgICAgICAgICAgICAgICAgICB0YWJsZU5hbWU6IHRhYmxlTmFtZSwKICAgICAgICAgICAgICAgICAgICAvLyDmmL7npLrmlofku7blkI3vvIjkuI3lkKvmianlsZXlkI3vvIkKICAgICAgICAgICAgICAgICAgICBmaWxlUGF0aDogZmlsZVBhdGgsCiAgICAgICAgICAgICAgICAgICAgLy8g5L+d5a2Y5a6M5pW06Lev5b6E55So5LqO5ZCO56uv5aSE55CGCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlRGF0ZTogJzIwMjQtMTItMjAgMTA6MDA6MDAnLAogICAgICAgICAgICAgICAgICAgIC8vIOWQjuerr+ayoeacieaPkOS+m+aXtumXtO+8jOS9v+eUqOm7mOiupOWAvAogICAgICAgICAgICAgICAgICAgIHJlY29yZENvdW50OiBudWxsLAogICAgICAgICAgICAgICAgICAgIC8vIOWQjuerr+ayoeacieaPkOS+m+iusOW9leaVsAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScKICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoIlx1NTJBMFx1OEY3RFx1NEU4NiAiLmNvbmNhdChfdGhpczIuYXZhaWxhYmxlVGFibGVzLmxlbmd0aCwgIiBcdTRFMkFFeGNlbFx1NjU4N1x1NEVGNiIpKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieaJvuWIsOWPr+eUqOeahEV4Y2Vs5paH5Lu2Jyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAxMDsKICAgICAgICAgICAgICBfY29udGV4dDIudDAgPSBfY29udGV4dDJbImNhdGNoIl0oMSk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L29RXhjZWzmlofku7bliJfooajlpLHotKU6JywgX2NvbnRleHQyLnQwKTsKICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vUV4Y2Vs5paH5Lu25YiX6KGo5aSx6LSlOiAnICsgX2NvbnRleHQyLnQwLm1lc3NhZ2UpOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMTQ7CiAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmdGaWxlcyA9IGZhbHNlOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuZmluaXNoKDE0KTsKICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgbnVsbCwgW1sxLCAxMCwgMTQsIDE3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDlpITnkIZFeGNlbOaWh+S7tumAieaLqeWPmOWMlgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUYWJsZXMgPSBzZWxlY3Rpb247CiAgICAgIGNvbnNvbGUubG9nKCflt7LpgInmi6lFeGNlbOaWh+S7tjonLCBzZWxlY3Rpb24pOwogICAgfSwKICAgIC8vIOenu+mZpOW3sumAieaLqeeahEV4Y2Vs5paH5Lu2CiAgICByZW1vdmVTZWxlY3RlZFRhYmxlOiBmdW5jdGlvbiByZW1vdmVTZWxlY3RlZFRhYmxlKHRhYmxlKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB2YXIgaW5kZXggPSB0aGlzLnNlbGVjdGVkVGFibGVzLmZpbmRJbmRleChmdW5jdGlvbiAodCkgewogICAgICAgIHJldHVybiB0LmlkID09PSB0YWJsZS5pZDsKICAgICAgfSk7CiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFRhYmxlcy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICAgIC8vIOWQjOaXtuabtOaWsOihqOagvOmAieaLqeeKtuaAgQogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIHRhYmxlUmVmID0gX3RoaXMzLiRyZWZzLnRhYmxlTGlzdDsKICAgICAgICBpZiAodGFibGVSZWYpIHsKICAgICAgICAgIHRhYmxlUmVmLnRvZ2dsZVJvd1NlbGVjdGlvbih0YWJsZSwgZmFsc2UpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5riF56m66YCJ5oupCiAgICBjbGVhclNlbGVjdGlvbjogZnVuY3Rpb24gY2xlYXJTZWxlY3Rpb24oKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnNlbGVjdGVkVGFibGVzID0gW107CiAgICAgIC8vIOa4heepuuihqOagvOmAieaLqQogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIHRhYmxlUmVmID0gX3RoaXM0LiRyZWZzLnRhYmxlTGlzdDsKICAgICAgICBpZiAodGFibGVSZWYpIHsKICAgICAgICAgIHRhYmxlUmVmLmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXnqbpFeGNlbOaWh+S7tumAieaLqScpOwogICAgfSwKICAgIHByb2Nlc3NTZWxlY3RlZFRhYmxlczogZnVuY3Rpb24gcHJvY2Vzc1NlbGVjdGVkVGFibGVzKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgcHJvZ3Jlc3NJbnRlcnZhbCwgZmlsZVBhdGhzLCByZXNwb25zZSwgZXhjZXB0aW9uTGlzdCwgYWxsQ29sdW1ucywgX2Vycm9yJHJlc3BvbnNlJGRhdGE7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGlmICghKF90aGlzNS5zZWxlY3RlZFRhYmxlcy5sZW5ndGggPT09IDApKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWkhOeQhueahEV4Y2Vs5paH5Lu2Jyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX3RoaXM1LnByb2Nlc3NpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF90aGlzNS5wcm9jZXNzUHJvZ3Jlc3MgPSAwOwogICAgICAgICAgICAgIF90aGlzNS5wcm9ncmVzc1RleHQgPSAn5byA5aeL5aSE55CGRXhjZWzmlofku7YuLi4nOwogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gNjsKICAgICAgICAgICAgICAvLyDov5vluqbmm7TmlrAKICAgICAgICAgICAgICBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgaWYgKF90aGlzNS5wcm9jZXNzUHJvZ3Jlc3MgPCA4MCkgewogICAgICAgICAgICAgICAgICBfdGhpczUucHJvY2Vzc1Byb2dyZXNzICs9IE1hdGgucmFuZG9tKCkgKiAxMDsKICAgICAgICAgICAgICAgICAgdmFyIGN1cnJlbnRTdGVwID0gTWF0aC5mbG9vcihfdGhpczUucHJvY2Vzc1Byb2dyZXNzIC8gMjUpOwogICAgICAgICAgICAgICAgICB2YXIgc3RlcHMgPSBbJ+ato+WcqOivu+WPlkV4Y2Vs5paH5Lu2Li4uJywgJ+ato+WcqOWQiOW5tuaVsOaNri4uLicsICfmraPlnKjliIbmnpDlvILluLguLi4nLCAn5aSE55CG5LitLi4uJ107CiAgICAgICAgICAgICAgICAgIF90aGlzNS5wcm9ncmVzc1RleHQgPSBzdGVwc1tjdXJyZW50U3RlcF0gfHwgJ+WkhOeQhuS4rS4uLic7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSwgNTAwKTsgLy8g6LCD55So5ZCO56uv5byC5bi45qOA5rWL5o6l5Y+jCiAgICAgICAgICAgICAgZmlsZVBhdGhzID0gX3RoaXM1LnNlbGVjdGVkVGFibGVzLm1hcChmdW5jdGlvbiAodCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHQuZmlsZVBhdGg7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+mAieS4reeahOihqOagvOaVsOaNrjonLCBfdGhpczUuc2VsZWN0ZWRUYWJsZXMpOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflj5HpgIHliLDlkI7nq6/nmoTmlofku7bot6/lvoQ6JywgZmlsZVBhdGhzKTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6L+Z5Lqb6Lev5b6E5p2l6IeqcGF0aF9kZWZhdWx05paH5Lu25aS5OicsIGZpbGVQYXRocyk7CiAgICAgICAgICAgICAgX3RoaXM1LnByb2dyZXNzVGV4dCA9ICfmraPlnKjosIPnlKjlkI7nq6/liIbmnpDmjqXlj6MuLi4nOwoKICAgICAgICAgICAgICAvLyDnnJ/mraPosIPnlKjlkI7nq69BUEkKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDE1OwogICAgICAgICAgICAgIHJldHVybiBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvZ2V0X3N1c19UcmFja2luZ051bScsIHsKICAgICAgICAgICAgICAgIGZpbGVuYW1lczogZmlsZVBhdGhzCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgICAgICBjbGVhckludGVydmFsKHByb2dyZXNzSW50ZXJ2YWwpOwogICAgICAgICAgICAgIF90aGlzNS5wcm9jZXNzUHJvZ3Jlc3MgPSAxMDA7CiAgICAgICAgICAgICAgX3RoaXM1LnByb2dyZXNzVGV4dCA9ICfmlbDmja7lpITnkIblrozmiJDvvIEnOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflkI7nq6/ov5Tlm57nmoTlvILluLjmo4DmtYvnu5Pmnpw6JywgcmVzcG9uc2UuZGF0YSk7CgogICAgICAgICAgICAgIC8vIOWkhOeQhuWQjuerr+i/lOWbnueahOW8guW4uOaVsOaNrgogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICAgICAgICBleGNlcHRpb25MaXN0ID0gW107CiAgICAgICAgICAgICAgICBhbGxDb2x1bW5zID0gbmV3IFNldCgpOyAvLyDnlKjkuo7mlLbpm4bmiYDmnInlj6/og73nmoTliJflkI0KICAgICAgICAgICAgICAgIC8vIOmBjeWOhuWQjuerr+i/lOWbnueahOWQhOenjeW8guW4uOexu+WeiwogICAgICAgICAgICAgICAgT2JqZWN0LmtleXMocmVzcG9uc2UuZGF0YSkuZm9yRWFjaChmdW5jdGlvbiAoZXhjZXB0aW9uVHlwZSkgewogICAgICAgICAgICAgICAgICB2YXIgZXhjZXB0aW9ucyA9IHJlc3BvbnNlLmRhdGFbZXhjZXB0aW9uVHlwZV07CiAgICAgICAgICAgICAgICAgIGlmIChleGNlcHRpb25zICYmIGV4Y2VwdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgIGV4Y2VwdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIOaUtumbhuaJgOacieWtl+auteWQjeS9nOS4uuWIl+WQjQogICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMoaXRlbSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGFsbENvbHVtbnMuYWRkKGtleSk7CiAgICAgICAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAgICAgICAvLyDnm7TmjqXkvb/nlKjlkI7nq6/ov5Tlm57nmoTmlbDmja7vvIzmt7vliqDlvILluLjnsbvlnosKICAgICAgICAgICAgICAgICAgICAgIHZhciBleGNlcHRpb24gPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGl0ZW0pLCB7fSwgewogICAgICAgICAgICAgICAgICAgICAgICAvLyDlsZXlvIDlkI7nq6/ov5Tlm57nmoTmiYDmnInlrZfmrrUKICAgICAgICAgICAgICAgICAgICAgICAg5byC5bi457G75Z6LOiBleGNlcHRpb25UeXBlIC8vIOa3u+WKoOW8guW4uOexu+Wei+Wtl+autQogICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICBleGNlcHRpb25MaXN0LnB1c2goZXhjZXB0aW9uKTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgLy8g55Sf5oiQ5Yqo5oCB6KGo5qC85YiXCiAgICAgICAgICAgICAgICBfdGhpczUuZXhjZXB0aW9uQ29sdW1ucyA9IFtdOwoKICAgICAgICAgICAgICAgIC8vIOmmluWFiOa3u+WKoOW8guW4uOexu+Wei+WIlwogICAgICAgICAgICAgICAgX3RoaXM1LmV4Y2VwdGlvbkNvbHVtbnMucHVzaCh7CiAgICAgICAgICAgICAgICAgIHByb3A6ICflvILluLjnsbvlnosnLAogICAgICAgICAgICAgICAgICBsYWJlbDogJ+W8guW4uOexu+WeiycsCiAgICAgICAgICAgICAgICAgIHdpZHRoOiAxNTAsCiAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgdHlwZTogJ3RhZycKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgIC8vIOeEtuWQjua3u+WKoOWFtuS7luWIlwogICAgICAgICAgICAgICAgQXJyYXkuZnJvbShhbGxDb2x1bW5zKS5mb3JFYWNoKGZ1bmN0aW9uIChjb2x1bW5OYW1lKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5leGNlcHRpb25Db2x1bW5zLnB1c2goewogICAgICAgICAgICAgICAgICAgIHByb3A6IGNvbHVtbk5hbWUsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGNvbHVtbk5hbWUsCiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IF90aGlzNS5nZXRDb2x1bW5XaWR0aChjb2x1bW5OYW1lKSwKICAgICAgICAgICAgICAgICAgICBhbGlnbjogX3RoaXM1LmdldENvbHVtbkFsaWduKGNvbHVtbk5hbWUpCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBfdGhpczUuZXhjZXB0aW9uTGlzdCA9IGV4Y2VwdGlvbkxpc3Q7CiAgICAgICAgICAgICAgICBpZiAoZXhjZXB0aW9uTGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5zdWNjZXNzKCJcdTYyMTBcdTUyOUZcdTU5MDRcdTc0MDYgIi5jb25jYXQoX3RoaXM1LnNlbGVjdGVkVGFibGVzLmxlbmd0aCwgIiBcdTRFMkFFeGNlbFx1NjU4N1x1NEVGNlx1RkYwQ1x1NTNEMVx1NzNCMCAiKS5jb25jYXQoZXhjZXB0aW9uTGlzdC5sZW5ndGgsICIgXHU2NzYxXHU1RjAyXHU1RTM4XHU2NTcwXHU2MzZFIikpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmluZm8oIlx1NjIxMFx1NTI5Rlx1NTkwNFx1NzQwNiAiLmNvbmNhdChfdGhpczUuc2VsZWN0ZWRUYWJsZXMubGVuZ3RoLCAiIFx1NEUyQUV4Y2VsXHU2NTg3XHU0RUY2XHVGRjBDXHU2NzJBXHU1M0QxXHU3M0IwXHU1RjAyXHU1RTM4XHU2NTcwXHU2MzZFIikpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2Uud2FybmluZygn5ZCO56uv6L+U5Zue5pWw5o2u5qC85byP5byC5bi4Jyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMjk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjM6CiAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSAyMzsKICAgICAgICAgICAgICBfY29udGV4dDMudDAgPSBfY29udGV4dDNbImNhdGNoIl0oNik7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5aSE55CG5aSx6LSlOicsIF9jb250ZXh0My50MCk7CiAgICAgICAgICAgICAgX3RoaXM1LnByb2Nlc3NQcm9ncmVzcyA9IDA7CiAgICAgICAgICAgICAgX3RoaXM1LnByb2dyZXNzVGV4dCA9ICcnOwogICAgICAgICAgICAgIGlmIChfY29udGV4dDMudDAucmVzcG9uc2UpIHsKICAgICAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigiXHU1OTA0XHU3NDA2XHU1OTMxXHU4RDI1OiAiLmNvbmNhdChfY29udGV4dDMudDAucmVzcG9uc2Uuc3RhdHVzLCAiIC0gIikuY29uY2F0KCgoX2Vycm9yJHJlc3BvbnNlJGRhdGEgPSBfY29udGV4dDMudDAucmVzcG9uc2UuZGF0YSkgPT09IG51bGwgfHwgX2Vycm9yJHJlc3BvbnNlJGRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lcnJvciRyZXNwb25zZSRkYXRhLm1lc3NhZ2UpIHx8IF9jb250ZXh0My50MC5tZXNzYWdlKSk7CiAgICAgICAgICAgICAgfSBlbHNlIGlmIChfY29udGV4dDMudDAucmVxdWVzdCkgewogICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKCfnvZHnu5zov57mjqXlpLHotKXvvIzor7fmo4Dmn6XlkI7nq6/mnI3liqHmmK/lkKblkK/liqgnKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKCJcdTU5MDRcdTc0MDZcdTU5MzFcdThEMjU6ICIuY29uY2F0KF9jb250ZXh0My50MC5tZXNzYWdlKSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDI5OgogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gMjk7CiAgICAgICAgICAgICAgX3RoaXM1LnByb2Nlc3NpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzNS5wcm9jZXNzUHJvZ3Jlc3MgPSAwOwogICAgICAgICAgICAgICAgX3RoaXM1LnByb2dyZXNzVGV4dCA9ICcnOwogICAgICAgICAgICAgIH0sIDMwMDApOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuZmluaXNoKDI5KTsKICAgICAgICAgICAgY2FzZSAzMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMywgbnVsbCwgW1s2LCAyMywgMjksIDMzXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBoYW5kbGVTY3JvbGw6IGZ1bmN0aW9uIGhhbmRsZVNjcm9sbChldmVudCkgewogICAgICAvLyDlpITnkIbmu5rliqjkuovku7YKICAgICAgY29uc29sZS5sb2coJ1Njcm9sbGluZy4uLicsIGV2ZW50KTsKICAgIH0sCiAgICAvLyDmoLnmja7lvILluLjnsbvlnovov5Tlm57lr7nlupTnmoTmoIfnrb7popzoibIKICAgIGdldEV4Y2VwdGlvblR5cGVDb2xvcjogZnVuY3Rpb24gZ2V0RXhjZXB0aW9uVHlwZUNvbG9yKGV4Y2VwdGlvblR5cGUpIHsKICAgICAgdmFyIGNvbG9yTWFwID0gewogICAgICAgICflkIzkuIDlp5PlkI3lpJrkuKrouqvku73or4EnOiAnZGFuZ2VyJywKICAgICAgICAn5ZCM5LiA6Lqr5Lu96K+B5aSa5Liq5aeT5ZCNJzogJ3dhcm5pbmcnLAogICAgICAgICfnianmtYHljZXlj7fph43lpI0nOiAnaW5mbycsCiAgICAgICAgJ+iuouWNleWPt+WkmuS4qui6q+S7veivgSc6ICdzdWNjZXNzJwogICAgICB9OwogICAgICByZXR1cm4gY29sb3JNYXBbZXhjZXB0aW9uVHlwZV0gfHwgJ3ByaW1hcnknOwogICAgfSwKICAgIC8vIOagueaNruWIl+WQjeiOt+WPluWIl+WuveW6pgogICAgZ2V0Q29sdW1uV2lkdGg6IGZ1bmN0aW9uIGdldENvbHVtbldpZHRoKGNvbHVtbk5hbWUpIHsKICAgICAgdmFyIHdpZHRoTWFwID0gewogICAgICAgICforqLljZXlj7cnOiAxODAsCiAgICAgICAgJ+aUr+S7mOS6uuWnk+WQjSc6IDEyMCwKICAgICAgICAn5pSv5LuY5Lq66Lqr5Lu96K+B5Y+3JzogMTgwLAogICAgICAgICfnianmtYHljZXlj7cnOiAxODAsCiAgICAgICAgJ+W8guW4uOexu+Weiyc6IDE1MAogICAgICB9OwogICAgICByZXR1cm4gd2lkdGhNYXBbY29sdW1uTmFtZV0gfHwgMTIwOwogICAgfSwKICAgIC8vIOagueaNruWIl+WQjeiOt+WPluWvuem9kOaWueW8jwogICAgZ2V0Q29sdW1uQWxpZ246IGZ1bmN0aW9uIGdldENvbHVtbkFsaWduKGNvbHVtbk5hbWUpIHsKICAgICAgdmFyIGFsaWduTWFwID0gewogICAgICAgICforqLljZXlj7cnOiAnY2VudGVyJywKICAgICAgICAn5pSv5LuY5Lq65aeT5ZCNJzogJ2NlbnRlcicsCiAgICAgICAgJ+aUr+S7mOS6uui6q+S7veivgeWPtyc6ICdjZW50ZXInLAogICAgICAgICfnianmtYHljZXlj7cnOiAnY2VudGVyJywKICAgICAgICAn5byC5bi457G75Z6LJzogJ2NlbnRlcicKICAgICAgfTsKICAgICAgcmV0dXJuIGFsaWduTWFwW2NvbHVtbk5hbWVdIHx8ICdsZWZ0JzsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["axios", "name", "data", "uploadFileList", "uploading", "uploadProgress", "uploadProgressText", "availableTables", "selectedTables", "loadingFiles", "processing", "processProgress", "progressText", "exceptionList", "exceptionColumns", "scrollContainer", "mounted", "loadAvailableFiles", "methods", "handleFileChange", "file", "fileList", "console", "log", "handleFileRemove", "beforeUpload", "isExcel", "type", "isLt10M", "size", "$message", "error", "clearUploadFiles", "$refs", "upload", "clearFiles", "info", "handleUpload", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "formData", "progressInterval", "response", "_response$data", "wrap", "_callee$", "_context", "prev", "next", "length", "warning", "abrupt", "FormData", "for<PERSON>ach", "fileItem", "index", "append", "raw", "setInterval", "Math", "random", "concat", "round", "post", "headers", "timeout", "sent", "success", "Error", "message", "t0", "status", "warn", "Promise", "resolve", "setTimeout", "clearInterval", "t1", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "paths", "map", "filePath", "fileName", "split", "pop", "tableName", "replace", "id", "createDate", "recordCount", "handleSelectionChange", "selection", "removeSelectedTable", "table", "_this3", "findIndex", "t", "splice", "$nextTick", "tableRef", "tableList", "toggleRowSelection", "clearSelection", "_this4", "processSelectedTables", "_this5", "_callee3", "filePaths", "allColumns", "_error$response$data", "_callee3$", "_context3", "currentStep", "floor", "steps", "filenames", "Set", "Object", "keys", "exceptionType", "exceptions", "item", "key", "add", "exception", "_objectSpread", "异常类型", "push", "prop", "label", "width", "align", "Array", "from", "columnName", "getColumnWidth", "getColumnAlign", "request", "handleScroll", "event", "getExceptionTypeColor", "colorMap", "widthMap", "alignMap"], "sources": ["src/components/Charts/OrderException.vue"], "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column\r\nv-for=\"column in exceptionColumns\"\r\n:key=\"column.prop\"\r\n:prop=\"column.prop\"\r\n:label=\"column.label\"\r\n:width=\"column.width\"\r\n:align=\"column.align\"\r\n>\r\n<template #default=\"{row}\">\r\n<el-tag\r\nv-if=\"column.type === 'tag'\"\r\n:type=\"getExceptionTypeColor(row[column.prop])\"\r\nsize=\"small\"\r\n>\r\n{{ row[column.prop] }}\r\n</el-tag>\r\n<span v-else>{{ row[column.prop] || '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n          const allColumns = new Set() // 用于收集所有可能的列名\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 收集所有字段名作为列名\r\n                Object.keys(item).forEach(key => {\r\n                  allColumns.add(key)\r\n                })\r\n\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  ...item, // 展开后端返回的所有字段\r\n                  异常类型: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 生成动态表格列\r\n          this.exceptionColumns = []\r\n\r\n          // 首先添加异常类型列\r\n          this.exceptionColumns.push({\r\n            prop: '异常类型',\r\n            label: '异常类型',\r\n            width: 150,\r\n            align: 'center',\r\n            type: 'tag'\r\n          })\r\n\r\n          // 然后添加其他列\r\n          Array.from(allColumns).forEach(columnName => {\r\n            this.exceptionColumns.push({\r\n              prop: columnName,\r\n              label: columnName,\r\n              width: this.getColumnWidth(columnName),\r\n              align: this.getColumnAlign(columnName)\r\n            })\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA;MACAC,SAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,eAAA;MAAA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,aAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAH,aAAA;IACA;IACA,KAAAI,kBAAA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,QAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAAJ,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,WAAAH,IAAA,CAAAnB,IAAA;IACA;IAEAwB,YAAA,WAAAA,aAAAL,IAAA;MACA,IAAAM,OAAA,GAAAN,IAAA,CAAAO,IAAA,4EACAP,IAAA,CAAAO,IAAA;MACA,IAAAC,OAAA,GAAAR,IAAA,CAAAS,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAH,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA;MACA,KAAA7B,cAAA;MACA,KAAA8B,KAAA,CAAAC,MAAA,CAAAC,UAAA;MACA,KAAAL,QAAA,CAAAM,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,gBAAA,EAAAC,QAAA,EAAAC,cAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAb,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;gBAAAH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAb,KAAA,CAAAR,QAAA,CAAAuB,OAAA;cAAA,OAAAJ,QAAA,CAAAK,MAAA;YAAA;cAIAhB,KAAA,CAAAlC,SAAA;cACAkC,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cAAA2C,QAAA,CAAAC,IAAA;cAGAP,QAAA,OAAAY,QAAA,IAEA;cACAjB,KAAA,CAAAnC,cAAA,CAAAqD,OAAA,WAAAC,QAAA,EAAAC,KAAA;gBACAf,QAAA,CAAAgB,MAAA,UAAAF,QAAA,CAAAG,GAAA;cACA;;cAEA;cACAhB,gBAAA,GAAAiB,WAAA;gBACA,IAAAvB,KAAA,CAAAjC,cAAA;kBACAiC,KAAA,CAAAjC,cAAA,IAAAyD,IAAA,CAAAC,MAAA;kBACAzB,KAAA,CAAAhC,kBAAA,8CAAA0D,MAAA,CAAAF,IAAA,CAAAG,KAAA,CAAA3B,KAAA,CAAAjC,cAAA;gBACA;cACA,SAEA;cACA;cAAA4C,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAnD,KAAA,CAAAkE,IAAA,uCAAAvB,QAAA;gBACAwB,OAAA;kBACA;gBACA;gBACAC,OAAA;cACA;YAAA;cALAvB,QAAA,GAAAI,QAAA,CAAAoB,IAAA;cAAA,MAQA,CAAAxB,QAAA,CAAA3C,IAAA,KAAA2C,QAAA,CAAA3C,IAAA,CAAAoE,OAAA;gBAAArB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoB,KAAA,GAAAzB,cAAA,GAAAD,QAAA,CAAA3C,IAAA,cAAA4C,cAAA,uBAAAA,cAAA,CAAA0B,OAAA;YAAA;cAAAvB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAwB,EAAA,GAAAxB,QAAA;cAAA,MAIAA,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,IAAAI,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,CAAA6B,MAAA;gBAAAzB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAqD,IAAA;cAAA1B,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAyB,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAAA5B,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,MAAAF,QAAA,CAAAwB,EAAA;YAAA;cAMAM,aAAA,CAAAnC,gBAAA;cACAN,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;;cAEA;cAAA2C,QAAA,CAAAE,IAAA;cAAA,OACAb,KAAA,CAAArB,kBAAA;YAAA;cAEAqB,KAAA,CAAAR,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAA1B,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;cACAd,KAAA,CAAAN,gBAAA;cAAAiB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAA+B,EAAA,GAAA/B,QAAA;cAEA3B,OAAA,CAAAS,KAAA,UAAAkB,QAAA,CAAA+B,EAAA;cACA1C,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cACAgC,KAAA,CAAAR,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAf,QAAA,CAAA+B,EAAA,CAAAR,OAAA;YAAA;cAAAvB,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAlC,SAAA;cACA0E,UAAA;gBACAxC,KAAA,CAAAjC,cAAA;gBACAiC,KAAA,CAAAhC,kBAAA;cACA;cAAA,OAAA2C,QAAA,CAAAgC,MAAA;YAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAiC,IAAA;UAAA;QAAA,GAAAxC,OAAA;MAAA;IAEA;IAEA;IACAzB,kBAAA,WAAAA,mBAAA;MAAA,IAAAkE,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAvC,QAAA;QAAA,OAAAL,mBAAA,GAAAO,IAAA,UAAAsC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;YAAA;cACAgC,MAAA,CAAA1E,YAAA;cAAA6E,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAnC,IAAA;cAAA,OAGAnD,KAAA,CAAAkE,IAAA;YAAA;cAAArB,QAAA,GAAAyC,SAAA,CAAAjB,IAAA;cACA/C,OAAA,CAAAC,GAAA,oBAAAsB,QAAA,CAAA3C,IAAA;cACAoB,OAAA,CAAAC,GAAA,2BAAAsB,QAAA,CAAA3C,IAAA,CAAAqF,KAAA;cAEA,IAAA1C,QAAA,CAAA3C,IAAA,IAAA2C,QAAA,CAAA3C,IAAA,CAAAqF,KAAA;gBACA;gBACAJ,MAAA,CAAA5E,eAAA,GAAAsC,QAAA,CAAA3C,IAAA,CAAAqF,KAAA,CAAAC,GAAA,WAAAC,QAAA,EAAA/B,KAAA;kBACA;kBACA,IAAAgC,QAAA,GAAAD,QAAA,CAAAE,KAAA,OAAAC,GAAA,MAAAH,QAAA,CAAAE,KAAA,MAAAC,GAAA;kBACA,IAAAC,SAAA,GAAAH,QAAA,CAAAI,OAAA;;kBAEA;oBACAC,EAAA,EAAArC,KAAA;oBACAmC,SAAA,EAAAA,SAAA;oBAAA;oBACAJ,QAAA,EAAAA,QAAA;oBAAA;oBACAO,UAAA;oBAAA;oBACAC,WAAA;oBAAA;oBACAvB,MAAA;kBACA;gBACA;gBACAS,MAAA,CAAArD,QAAA,CAAAwC,OAAA,uBAAAN,MAAA,CAAAmB,MAAA,CAAA5E,eAAA,CAAA6C,MAAA;cACA;gBACA+B,MAAA,CAAArD,QAAA,CAAAuB,OAAA;cACA;cAAAiC,SAAA,CAAAnC,IAAA;cAAA;YAAA;cAAAmC,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAEAhE,OAAA,CAAAS,KAAA,mBAAAuD,SAAA,CAAAb,EAAA;cACAU,MAAA,CAAArD,QAAA,CAAAC,KAAA,qBAAAuD,SAAA,CAAAb,EAAA,CAAAD,OAAA;YAAA;cAAAc,SAAA,CAAApC,IAAA;cAEAiC,MAAA,CAAA1E,YAAA;cAAA,OAAA6E,SAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAc,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3F,cAAA,GAAA2F,SAAA;MACA7E,OAAA,CAAAC,GAAA,gBAAA4E,SAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAA5C,KAAA,QAAAlD,cAAA,CAAA+F,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAT,EAAA,KAAAM,KAAA,CAAAN,EAAA;MAAA;MACA,IAAArC,KAAA;QACA,KAAAlD,cAAA,CAAAiG,MAAA,CAAA/C,KAAA;MACA;MACA;MACA,KAAAgD,SAAA;QACA,IAAAC,QAAA,GAAAL,MAAA,CAAArE,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAE,kBAAA,CAAAR,KAAA;QACA;MACA;IACA;IAEA;IACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAvG,cAAA;MACA;MACA,KAAAkG,SAAA;QACA,IAAAC,QAAA,GAAAI,MAAA,CAAA9E,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAG,cAAA;QACA;MACA;MACA,KAAAhF,QAAA,CAAAM,IAAA;IACA;IACA4E,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA1E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyE,SAAA;QAAA,IAAAtE,gBAAA,EAAAuE,SAAA,EAAAtE,QAAA,EAAAhC,aAAA,EAAAuG,UAAA,EAAAC,oBAAA;QAAA,OAAA7E,mBAAA,GAAAO,IAAA,UAAAuE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;YAAA;cAAA,MACA8D,MAAA,CAAAzG,cAAA,CAAA4C,MAAA;gBAAAmE,SAAA,CAAApE,IAAA;gBAAA;cAAA;cACA8D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cAAA,OAAAkE,SAAA,CAAAjE,MAAA;YAAA;cAIA2D,MAAA,CAAAvG,UAAA;cACAuG,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAAA2G,SAAA,CAAArE,IAAA;cAGA;cACAN,gBAAA,GAAAiB,WAAA;gBACA,IAAAoD,MAAA,CAAAtG,eAAA;kBACAsG,MAAA,CAAAtG,eAAA,IAAAmD,IAAA,CAAAC,MAAA;kBACA,IAAAyD,WAAA,GAAA1D,IAAA,CAAA2D,KAAA,CAAAR,MAAA,CAAAtG,eAAA;kBACA,IAAA+G,KAAA;kBACAT,MAAA,CAAArG,YAAA,GAAA8G,KAAA,CAAAF,WAAA;gBACA;cACA,SAEA;cACAL,SAAA,GAAAF,MAAA,CAAAzG,cAAA,CAAAgF,GAAA,WAAAgB,CAAA;gBAAA,OAAAA,CAAA,CAAAf,QAAA;cAAA;cACAnE,OAAA,CAAAC,GAAA,aAAA0F,MAAA,CAAAzG,cAAA;cACAc,OAAA,CAAAC,GAAA,gBAAA4F,SAAA;cACA7F,OAAA,CAAAC,GAAA,2BAAA4F,SAAA;cAEAF,MAAA,CAAArG,YAAA;;cAEA;cAAA2G,SAAA,CAAApE,IAAA;cAAA,OACAnD,KAAA,CAAAkE,IAAA;gBACAyD,SAAA,EAAAR;cACA;YAAA;cAFAtE,QAAA,GAAA0E,SAAA,CAAAlD,IAAA;cAIAU,aAAA,CAAAnC,gBAAA;cACAqE,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAEAU,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA3C,IAAA;;cAEA;cACA,IAAA2C,QAAA,CAAA3C,IAAA;gBACAW,aAAA;gBACAuG,UAAA,OAAAQ,GAAA;gBAEA;gBACAC,MAAA,CAAAC,IAAA,CAAAjF,QAAA,CAAA3C,IAAA,EAAAsD,OAAA,WAAAuE,aAAA;kBACA,IAAAC,UAAA,GAAAnF,QAAA,CAAA3C,IAAA,CAAA6H,aAAA;kBACA,IAAAC,UAAA,IAAAA,UAAA,CAAA5E,MAAA;oBACA4E,UAAA,CAAAxE,OAAA,WAAAyE,IAAA,EAAAvE,KAAA;sBACA;sBACAmE,MAAA,CAAAC,IAAA,CAAAG,IAAA,EAAAzE,OAAA,WAAA0E,GAAA;wBACAd,UAAA,CAAAe,GAAA,CAAAD,GAAA;sBACA;;sBAEA;sBACA,IAAAE,SAAA,GAAAC,aAAA,CAAAA,aAAA,KACAJ,IAAA;wBAAA;wBACAK,IAAA,EAAAP,aAAA;sBAAA,EACA;sBACAlH,aAAA,CAAA0H,IAAA,CAAAH,SAAA;oBACA;kBACA;gBACA;;gBAEA;gBACAnB,MAAA,CAAAnG,gBAAA;;gBAEA;gBACAmG,MAAA,CAAAnG,gBAAA,CAAAyH,IAAA;kBACAC,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAhH,IAAA;gBACA;;gBAEA;gBACAiH,KAAA,CAAAC,IAAA,CAAAzB,UAAA,EAAA5D,OAAA,WAAAsF,UAAA;kBACA7B,MAAA,CAAAnG,gBAAA,CAAAyH,IAAA;oBACAC,IAAA,EAAAM,UAAA;oBACAL,KAAA,EAAAK,UAAA;oBACAJ,KAAA,EAAAzB,MAAA,CAAA8B,cAAA,CAAAD,UAAA;oBACAH,KAAA,EAAA1B,MAAA,CAAA+B,cAAA,CAAAF,UAAA;kBACA;gBACA;gBAEA7B,MAAA,CAAApG,aAAA,GAAAA,aAAA;gBAEA,IAAAA,aAAA,CAAAuC,MAAA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAAiD,MAAA,CAAAzG,cAAA,CAAA4C,MAAA,iDAAAY,MAAA,CAAAnD,aAAA,CAAAuC,MAAA;gBACA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAM,IAAA,6BAAA4B,MAAA,CAAAiD,MAAA,CAAAzG,cAAA,CAAA4C,MAAA;gBACA;cACA;gBACA6D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cACA;cAAAkE,SAAA,CAAApE,IAAA;cAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAA9C,EAAA,GAAA8C,SAAA;cAEAjG,OAAA,CAAAS,KAAA,UAAAwF,SAAA,CAAA9C,EAAA;cACAwC,MAAA,CAAAtG,eAAA;cACAsG,MAAA,CAAArG,YAAA;cAEA,IAAA2G,SAAA,CAAA9C,EAAA,CAAA5B,QAAA;gBACAoE,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAuD,SAAA,CAAA9C,EAAA,CAAA5B,QAAA,CAAA6B,MAAA,SAAAV,MAAA,GAAAqD,oBAAA,GAAAE,SAAA,CAAA9C,EAAA,CAAA5B,QAAA,CAAA3C,IAAA,cAAAmH,oBAAA,uBAAAA,oBAAA,CAAA7C,OAAA,KAAA+C,SAAA,CAAA9C,EAAA,CAAAD,OAAA;cACA,WAAA+C,SAAA,CAAA9C,EAAA,CAAAwE,OAAA;gBACAhC,MAAA,CAAAnF,QAAA,CAAAC,KAAA;cACA;gBACAkF,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAuD,SAAA,CAAA9C,EAAA,CAAAD,OAAA;cACA;YAAA;cAAA+C,SAAA,CAAArE,IAAA;cAEA+D,MAAA,CAAAvG,UAAA;cACAoE,UAAA;gBACAmC,MAAA,CAAAtG,eAAA;gBACAsG,MAAA,CAAArG,YAAA;cACA;cAAA,OAAA2G,SAAA,CAAAtC,MAAA;YAAA;YAAA;cAAA,OAAAsC,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IAEAgC,YAAA,WAAAA,aAAAC,KAAA;MACA;MACA7H,OAAA,CAAAC,GAAA,iBAAA4H,KAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAArB,aAAA;MACA,IAAAsB,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAtB,aAAA;IACA;IAEA;IACAgB,cAAA,WAAAA,eAAAD,UAAA;MACA,IAAAQ,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAR,UAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAF,UAAA;MACA,IAAAS,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAT,UAAA;IACA;EACA;AACA", "ignoreList": []}]}