{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749131703482}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "name", "data", "uploadFileList", "uploading", "uploadProgress", "uploadProgressText", "availableTables", "selectedTables", "loadingFiles", "processing", "processProgress", "progressText", "exceptionList", "scrollContainer", "mounted", "loadAvailableFiles", "methods", "handleFileChange", "file", "fileList", "console", "log", "handleFileRemove", "beforeUpload", "isExcel", "type", "isLt10M", "size", "$message", "error", "clearUploadFiles", "$refs", "upload", "clearFiles", "info", "handleUpload", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "formData", "progressInterval", "response", "_response$data", "wrap", "_callee$", "_context", "prev", "next", "length", "warning", "abrupt", "FormData", "for<PERSON>ach", "fileItem", "index", "append", "raw", "setInterval", "Math", "random", "concat", "round", "post", "headers", "timeout", "sent", "success", "Error", "message", "t0", "status", "warn", "Promise", "resolve", "setTimeout", "clearInterval", "t1", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "paths", "map", "filePath", "fileName", "split", "pop", "tableName", "replace", "id", "createDate", "recordCount", "handleSelectionChange", "selection", "removeSelectedTable", "table", "_this3", "findIndex", "t", "splice", "$nextTick", "tableRef", "tableList", "toggleRowSelection", "clearSelection", "_this4", "processSelectedTables", "_this5", "_callee3", "filePaths", "_error$response$data", "_callee3$", "_context3", "currentStep", "floor", "steps", "filenames", "Object", "keys", "exceptionType", "exceptions", "item", "exception", "orderNo", "Date", "now", "category", "specs", "unitPrice", "quantity", "totalAmount", "payerName", "idNumber", "phone", "orderDate", "toISOString", "orderTime", "toTimeString", "paymentDate", "paymentTime", "logisticsNo", "push", "request", "handleScroll", "event", "getExceptionTypeColor", "colorMap"], "sources": ["src/components/Charts/OrderException.vue"], "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"exceptionType\" label=\"异常类型\" width=\"150\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"getExceptionTypeColor(row.exceptionType)\" size=\"small\">\r\n{{ row.exceptionType }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 根据后端返回的数据结构转换为前端显示格式\r\n                const exception = {\r\n                  orderNo: item['订单号'] || `异常-${Date.now()}-${index}`,\r\n                  category: exceptionType, // 异常类型作为分类\r\n                  specs: `${exceptionType}异常`,\r\n                  unitPrice: 0,\r\n                  quantity: 1,\r\n                  totalAmount: 0,\r\n                  payerName: item['支付人姓名'] || '未知',\r\n                  idNumber: item['支付人身份证号'] || '未知',\r\n                  phone: '未提供',\r\n                  orderDate: new Date().toISOString().split('T')[0],\r\n                  orderTime: new Date().toTimeString().split(' ')[0],\r\n                  paymentDate: new Date().toISOString().split('T')[0],\r\n                  paymentTime: new Date().toTimeString().split(' ')[0],\r\n                  logisticsNo: item['物流单号'] || '未知',\r\n                  exceptionType: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA;MACAC,SAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,eAAA;MAAA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,aAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAF,aAAA;IACA;IACA,KAAAG,kBAAA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAjB,cAAA,GAAAiB,QAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,QAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAAJ,IAAA,EAAAC,QAAA;MACA,KAAAjB,cAAA,GAAAiB,QAAA;MACAC,OAAA,CAAAC,GAAA,WAAAH,IAAA,CAAAlB,IAAA;IACA;IAEAuB,YAAA,WAAAA,aAAAL,IAAA;MACA,IAAAM,OAAA,GAAAN,IAAA,CAAAO,IAAA,4EACAP,IAAA,CAAAO,IAAA;MACA,IAAAC,OAAA,GAAAR,IAAA,CAAAS,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAH,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA;MACA,KAAA5B,cAAA;MACA,KAAA6B,KAAA,CAAAC,MAAA,CAAAC,UAAA;MACA,KAAAL,QAAA,CAAAM,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,gBAAA,EAAAC,QAAA,EAAAC,cAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAb,KAAA,CAAAlC,cAAA,CAAAgD,MAAA;gBAAAH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAb,KAAA,CAAAR,QAAA,CAAAuB,OAAA;cAAA,OAAAJ,QAAA,CAAAK,MAAA;YAAA;cAIAhB,KAAA,CAAAjC,SAAA;cACAiC,KAAA,CAAAhC,cAAA;cACAgC,KAAA,CAAA/B,kBAAA;cAAA0C,QAAA,CAAAC,IAAA;cAGAP,QAAA,OAAAY,QAAA,IAEA;cACAjB,KAAA,CAAAlC,cAAA,CAAAoD,OAAA,WAAAC,QAAA,EAAAC,KAAA;gBACAf,QAAA,CAAAgB,MAAA,UAAAF,QAAA,CAAAG,GAAA;cACA;;cAEA;cACAhB,gBAAA,GAAAiB,WAAA;gBACA,IAAAvB,KAAA,CAAAhC,cAAA;kBACAgC,KAAA,CAAAhC,cAAA,IAAAwD,IAAA,CAAAC,MAAA;kBACAzB,KAAA,CAAA/B,kBAAA,8CAAAyD,MAAA,CAAAF,IAAA,CAAAG,KAAA,CAAA3B,KAAA,CAAAhC,cAAA;gBACA;cACA,SAEA;cACA;cAAA2C,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAlD,KAAA,CAAAiE,IAAA,uCAAAvB,QAAA;gBACAwB,OAAA;kBACA;gBACA;gBACAC,OAAA;cACA;YAAA;cALAvB,QAAA,GAAAI,QAAA,CAAAoB,IAAA;cAAA,MAQA,CAAAxB,QAAA,CAAA1C,IAAA,KAAA0C,QAAA,CAAA1C,IAAA,CAAAmE,OAAA;gBAAArB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoB,KAAA,GAAAzB,cAAA,GAAAD,QAAA,CAAA1C,IAAA,cAAA2C,cAAA,uBAAAA,cAAA,CAAA0B,OAAA;YAAA;cAAAvB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAwB,EAAA,GAAAxB,QAAA;cAAA,MAIAA,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,IAAAI,QAAA,CAAAwB,EAAA,CAAA5B,QAAA,CAAA6B,MAAA;gBAAAzB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAqD,IAAA;cAAA1B,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAyB,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAAA5B,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,MAAAF,QAAA,CAAAwB,EAAA;YAAA;cAMAM,aAAA,CAAAnC,gBAAA;cACAN,KAAA,CAAAhC,cAAA;cACAgC,KAAA,CAAA/B,kBAAA;;cAEA;cAAA0C,QAAA,CAAAE,IAAA;cAAA,OACAb,KAAA,CAAArB,kBAAA;YAAA;cAEAqB,KAAA,CAAAR,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAA1B,KAAA,CAAAlC,cAAA,CAAAgD,MAAA;cACAd,KAAA,CAAAN,gBAAA;cAAAiB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAA+B,EAAA,GAAA/B,QAAA;cAEA3B,OAAA,CAAAS,KAAA,UAAAkB,QAAA,CAAA+B,EAAA;cACA1C,KAAA,CAAAhC,cAAA;cACAgC,KAAA,CAAA/B,kBAAA;cACA+B,KAAA,CAAAR,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAf,QAAA,CAAA+B,EAAA,CAAAR,OAAA;YAAA;cAAAvB,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAjC,SAAA;cACAyE,UAAA;gBACAxC,KAAA,CAAAhC,cAAA;gBACAgC,KAAA,CAAA/B,kBAAA;cACA;cAAA,OAAA0C,QAAA,CAAAgC,MAAA;YAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAiC,IAAA;UAAA;QAAA,GAAAxC,OAAA;MAAA;IAEA;IAEA;IACAzB,kBAAA,WAAAA,mBAAA;MAAA,IAAAkE,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAvC,QAAA;QAAA,OAAAL,mBAAA,GAAAO,IAAA,UAAAsC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;YAAA;cACAgC,MAAA,CAAAzE,YAAA;cAAA4E,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAnC,IAAA;cAAA,OAGAlD,KAAA,CAAAiE,IAAA;YAAA;cAAArB,QAAA,GAAAyC,SAAA,CAAAjB,IAAA;cACA/C,OAAA,CAAAC,GAAA,oBAAAsB,QAAA,CAAA1C,IAAA;cAEA,IAAA0C,QAAA,CAAA1C,IAAA,IAAA0C,QAAA,CAAA1C,IAAA,CAAAoF,KAAA;gBACA;gBACAJ,MAAA,CAAA3E,eAAA,GAAAqC,QAAA,CAAA1C,IAAA,CAAAoF,KAAA,CAAAC,GAAA,WAAAC,QAAA,EAAA/B,KAAA;kBACA;kBACA,IAAAgC,QAAA,GAAAD,QAAA,CAAAE,KAAA,OAAAC,GAAA,MAAAH,QAAA,CAAAE,KAAA,MAAAC,GAAA;kBACA,IAAAC,SAAA,GAAAH,QAAA,CAAAI,OAAA;;kBAEA;oBACAC,EAAA,EAAArC,KAAA;oBACAmC,SAAA,EAAAA,SAAA;oBAAA;oBACAJ,QAAA,EAAAA,QAAA;oBAAA;oBACAO,UAAA;oBAAA;oBACAC,WAAA;oBAAA;oBACAvB,MAAA;kBACA;gBACA;gBACAS,MAAA,CAAArD,QAAA,CAAAwC,OAAA,uBAAAN,MAAA,CAAAmB,MAAA,CAAA3E,eAAA,CAAA4C,MAAA;cACA;gBACA+B,MAAA,CAAArD,QAAA,CAAAuB,OAAA;cACA;cAAAiC,SAAA,CAAAnC,IAAA;cAAA;YAAA;cAAAmC,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAEAhE,OAAA,CAAAS,KAAA,mBAAAuD,SAAA,CAAAb,EAAA;cACAU,MAAA,CAAArD,QAAA,CAAAC,KAAA,qBAAAuD,SAAA,CAAAb,EAAA,CAAAD,OAAA;YAAA;cAAAc,SAAA,CAAApC,IAAA;cAEAiC,MAAA,CAAAzE,YAAA;cAAA,OAAA4E,SAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAc,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1F,cAAA,GAAA0F,SAAA;MACA7E,OAAA,CAAAC,GAAA,gBAAA4E,SAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAA5C,KAAA,QAAAjD,cAAA,CAAA8F,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAT,EAAA,KAAAM,KAAA,CAAAN,EAAA;MAAA;MACA,IAAArC,KAAA;QACA,KAAAjD,cAAA,CAAAgG,MAAA,CAAA/C,KAAA;MACA;MACA;MACA,KAAAgD,SAAA;QACA,IAAAC,QAAA,GAAAL,MAAA,CAAArE,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAE,kBAAA,CAAAR,KAAA;QACA;MACA;IACA;IAEA;IACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAtG,cAAA;MACA;MACA,KAAAiG,SAAA;QACA,IAAAC,QAAA,GAAAI,MAAA,CAAA9E,KAAA,CAAA2E,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAG,cAAA;QACA;MACA;MACA,KAAAhF,QAAA,CAAAM,IAAA;IACA;IACA4E,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA1E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyE,SAAA;QAAA,IAAAtE,gBAAA,EAAAuE,SAAA,EAAAtE,QAAA,EAAA/B,aAAA,EAAAsG,oBAAA;QAAA,OAAA5E,mBAAA,GAAAO,IAAA,UAAAsE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApE,IAAA,GAAAoE,SAAA,CAAAnE,IAAA;YAAA;cAAA,MACA8D,MAAA,CAAAxG,cAAA,CAAA2C,MAAA;gBAAAkE,SAAA,CAAAnE,IAAA;gBAAA;cAAA;cACA8D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cAAA,OAAAiE,SAAA,CAAAhE,MAAA;YAAA;cAIA2D,MAAA,CAAAtG,UAAA;cACAsG,MAAA,CAAArG,eAAA;cACAqG,MAAA,CAAApG,YAAA;cAAAyG,SAAA,CAAApE,IAAA;cAGA;cACAN,gBAAA,GAAAiB,WAAA;gBACA,IAAAoD,MAAA,CAAArG,eAAA;kBACAqG,MAAA,CAAArG,eAAA,IAAAkD,IAAA,CAAAC,MAAA;kBACA,IAAAwD,WAAA,GAAAzD,IAAA,CAAA0D,KAAA,CAAAP,MAAA,CAAArG,eAAA;kBACA,IAAA6G,KAAA;kBACAR,MAAA,CAAApG,YAAA,GAAA4G,KAAA,CAAAF,WAAA;gBACA;cACA,SAEA;cACAJ,SAAA,GAAAF,MAAA,CAAAxG,cAAA,CAAA+E,GAAA,WAAAgB,CAAA;gBAAA,OAAAA,CAAA,CAAAf,QAAA;cAAA;cACAnE,OAAA,CAAAC,GAAA,gBAAA4F,SAAA;cAEAF,MAAA,CAAApG,YAAA;;cAEA;cAAAyG,SAAA,CAAAnE,IAAA;cAAA,OACAlD,KAAA,CAAAiE,IAAA;gBACAwD,SAAA,EAAAP;cACA;YAAA;cAFAtE,QAAA,GAAAyE,SAAA,CAAAjD,IAAA;cAIAU,aAAA,CAAAnC,gBAAA;cACAqE,MAAA,CAAArG,eAAA;cACAqG,MAAA,CAAApG,YAAA;cAEAS,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA1C,IAAA;;cAEA;cACA,IAAA0C,QAAA,CAAA1C,IAAA;gBACAW,aAAA,OAEA;gBACA6G,MAAA,CAAAC,IAAA,CAAA/E,QAAA,CAAA1C,IAAA,EAAAqD,OAAA,WAAAqE,aAAA;kBACA,IAAAC,UAAA,GAAAjF,QAAA,CAAA1C,IAAA,CAAA0H,aAAA;kBACA,IAAAC,UAAA,IAAAA,UAAA,CAAA1E,MAAA;oBACA0E,UAAA,CAAAtE,OAAA,WAAAuE,IAAA,EAAArE,KAAA;sBACA;sBACA,IAAAsE,SAAA;wBACAC,OAAA,EAAAF,IAAA,2BAAA/D,MAAA,CAAAkE,IAAA,CAAAC,GAAA,SAAAnE,MAAA,CAAAN,KAAA;wBACA0E,QAAA,EAAAP,aAAA;wBAAA;wBACAQ,KAAA,KAAArE,MAAA,CAAA6D,aAAA;wBACAS,SAAA;wBACAC,QAAA;wBACAC,WAAA;wBACAC,SAAA,EAAAV,IAAA;wBACAW,QAAA,EAAAX,IAAA;wBACAY,KAAA;wBACAC,SAAA,MAAAV,IAAA,GAAAW,WAAA,GAAAlD,KAAA;wBACAmD,SAAA,MAAAZ,IAAA,GAAAa,YAAA,GAAApD,KAAA;wBACAqD,WAAA,MAAAd,IAAA,GAAAW,WAAA,GAAAlD,KAAA;wBACAsD,WAAA,MAAAf,IAAA,GAAAa,YAAA,GAAApD,KAAA;wBACAuD,WAAA,EAAAnB,IAAA;wBACAF,aAAA,EAAAA,aAAA;sBACA;sBACA/G,aAAA,CAAAqI,IAAA,CAAAnB,SAAA;oBACA;kBACA;gBACA;gBAEAf,MAAA,CAAAnG,aAAA,GAAAA,aAAA;gBAEA,IAAAA,aAAA,CAAAsC,MAAA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAwC,OAAA,6BAAAN,MAAA,CAAAiD,MAAA,CAAAxG,cAAA,CAAA2C,MAAA,iDAAAY,MAAA,CAAAlD,aAAA,CAAAsC,MAAA;gBACA;kBACA6D,MAAA,CAAAnF,QAAA,CAAAM,IAAA,6BAAA4B,MAAA,CAAAiD,MAAA,CAAAxG,cAAA,CAAA2C,MAAA;gBACA;cACA;gBACA6D,MAAA,CAAAnF,QAAA,CAAAuB,OAAA;cACA;cAAAiE,SAAA,CAAAnE,IAAA;cAAA;YAAA;cAAAmE,SAAA,CAAApE,IAAA;cAAAoE,SAAA,CAAA7C,EAAA,GAAA6C,SAAA;cAEAhG,OAAA,CAAAS,KAAA,UAAAuF,SAAA,CAAA7C,EAAA;cACAwC,MAAA,CAAArG,eAAA;cACAqG,MAAA,CAAApG,YAAA;cAEA,IAAAyG,SAAA,CAAA7C,EAAA,CAAA5B,QAAA;gBACAoE,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAsD,SAAA,CAAA7C,EAAA,CAAA5B,QAAA,CAAA6B,MAAA,SAAAV,MAAA,GAAAoD,oBAAA,GAAAE,SAAA,CAAA7C,EAAA,CAAA5B,QAAA,CAAA1C,IAAA,cAAAiH,oBAAA,uBAAAA,oBAAA,CAAA5C,OAAA,KAAA8C,SAAA,CAAA7C,EAAA,CAAAD,OAAA;cACA,WAAA8C,SAAA,CAAA7C,EAAA,CAAA2E,OAAA;gBACAnC,MAAA,CAAAnF,QAAA,CAAAC,KAAA;cACA;gBACAkF,MAAA,CAAAnF,QAAA,CAAAC,KAAA,8BAAAiC,MAAA,CAAAsD,SAAA,CAAA7C,EAAA,CAAAD,OAAA;cACA;YAAA;cAAA8C,SAAA,CAAApE,IAAA;cAEA+D,MAAA,CAAAtG,UAAA;cACAmE,UAAA;gBACAmC,MAAA,CAAArG,eAAA;gBACAqG,MAAA,CAAApG,YAAA;cACA;cAAA,OAAAyG,SAAA,CAAArC,MAAA;YAAA;YAAA;cAAA,OAAAqC,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IAEAmC,YAAA,WAAAA,aAAAC,KAAA;MACA;MACAhI,OAAA,CAAAC,GAAA,iBAAA+H,KAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA1B,aAAA;MACA,IAAA2B,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAA3B,aAAA;IACA;EACA;AACA", "ignoreList": []}]}