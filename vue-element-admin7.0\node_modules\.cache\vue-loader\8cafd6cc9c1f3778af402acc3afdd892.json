{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue", "mtime": 1748922947668}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RateGraph.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RateGraph.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" :disabled=\"!value\" @click=\"confirmSelection\">确认</el-button>\r\n    <el-button type=\"info\" :disabled=\"!value\" @click=\"resetWeights\">重置权重</el-button>\r\n\r\n    <!-- 权重设置区域 -->\r\n    <div class=\"weight-settings\">\r\n      <h3>标签权重设置</h3>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>快进快出：</span>\r\n          <el-slider v-model=\"weights.flag2\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>高频交易：</span>\r\n          <el-slider v-model=\"weights.flag3\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>时间集中：</span>\r\n          <el-slider v-model=\"weights.flag4\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>小额测试：</span>\r\n          <el-slider v-model=\"weights.flag5\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card style=\"margin: 20px; min-height: 520px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>嫌疑排行榜</span>\r\n      </div>\r\n\r\n      <div class=\"scroll-container\">\r\n        <div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n          <el-table :data=\"rankData\" style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"排名\" width=\"80\" />\r\n            <el-table-column prop=\"name\" label=\"姓名 / 公司名称\">\r\n              <template #default=\"scope\">\r\n                <el-link\r\n                  type=\"primary\"\r\n                  @click=\"handleNameClick(scope.row.name)\"\r\n                >\r\n                  {{ scope.row.name }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fused_score\" label=\"嫌疑分值\">\r\n              <template #default=\"scope\">\r\n                {{ Number(scope.row.fused_score).toFixed(3) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"count\" label=\"命中标签数\">\r\n              <template #default=\"scope\">{{ scope.row.count }}</template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"flag2\" label=\"快进快出\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag2 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag3\" label=\"高频交易\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag3 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag4\" label=\"时间集中\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag4 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag5\" label=\"小额测试\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag5 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <div\r\n          v-if=\"isBottom\"\r\n          class=\"bottom-notice\"\r\n          :class=\"{ 'show-notice': isBottom }\"\r\n        >\r\n          已经到达末尾\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  data() {\r\n    return {\r\n      options: [],\r\n      dateRange: [],\r\n      username: null,\r\n      isBottom: false,\r\n      weights: {\r\n        flag2: 1,\r\n        flag3: 1,\r\n        flag4: 1,\r\n        flag5: 1\r\n      },\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      rankData: state => state.rateGraph.rankData,\r\n      weights: state => state.rateGraph.weights,\r\n      dataLoaded: state => state.rateGraph.dataLoaded,\r\n      selectedTable: state => state.rateGraph.selectedTable\r\n    }),\r\n    value: {\r\n      get() {\r\n        return this.selectedTable\r\n      },\r\n      set(value) {\r\n        this.selectTable(value)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch()\r\n    this.scrollContainer = this.$refs.scrollContainer\r\n\r\n    // If we already have a selected table, update the dropdown\r\n    if (this.selectedTable) {\r\n      this.value = this.selectedTable\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateRankData: 'rateGraph/updateRankData',\r\n      selectTable: 'rateGraph/selectTable',\r\n      updateWeights: 'rateGraph/updateWeights',\r\n      resetWeightsAction: 'rateGraph/resetWeights'\r\n    }),\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      if (value) {\r\n        this.$message.info('已选择数据表，请点击「确认」按钮生成排行榜')\r\n      }\r\n    },\r\n    handleNameClick(name) {\r\n      this.$router.push({\r\n        path: '/charts/transmit-detail',\r\n        query: {\r\n          name: encodeURIComponent(name),\r\n          timeUnit: 'month'\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item,\r\n            value: item\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    async generateRankData(weights) {\r\n      try {\r\n        console.log(11)\r\n        if (weights) {\r\n          console.log('发送权重到后端:', weights)\r\n          try {\r\n            const res = await axios.post('http://127.0.0.1:8000/tianye_demo',\r\n              { weights: [this.weights.flag2, this.weights.flag3, this.weights.flag4, this.weights.flag5] }, // 数据\r\n              { // 配置\r\n                headers: {\r\n                  'Content-Type': 'application/json'\r\n                }\r\n              })\r\n            console.log('后端响应:', res)\r\n            console.log('后端', res.data.data)\r\n            this.updateRankData(res.data.data)\r\n          } catch (postError) {\r\n            const getRes = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n            this.updateRankData(getRes.data)\r\n          }\r\n        }\r\n        // } else {\r\n        //   console.log('默认')\r\n        //   try {\r\n        //     const res = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n        //     console.log('获取默认数据成功:', res)\r\n        //     this.updateRankData(res.data.data)\r\n        //   } catch (error) {\r\n        //     console.error('请求失败详细信息:', error)\r\n        //     if (error.response) {\r\n        //       console.error('服务器响应:', error.response.status, error.response.data)\r\n        //     } else if (error.request) {\r\n        //       console.error('没有收到响应，请求详情:', error.request)\r\n        //     } else {\r\n        //       console.error('请求设置错误:', error.message)\r\n        //     }\r\n        //     this.$message.error('连接后端失败，请检查网络和服务器状态')\r\n        //     throw error\r\n        //   }\r\n        // }\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('Error fetching rank data:', error)\r\n        this.$message.error('获取数据失败:', error.message)\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n    confirmSelection() {\r\n      // Only fetch data if we haven't loaded it yet or if the user explicitly wants to refresh\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: '正在处理数据...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(255, 255, 255, 0.7)'\r\n      })\r\n\r\n      // Update the weights in the store\r\n      this.updateWeights(this.weights)\r\n\r\n      this.generateRankData(this.weights)\r\n        .then(() => {\r\n          loading.close()\r\n          this.$message.success('数据处理成功')\r\n        })\r\n        .catch(() => {\r\n          loading.close()\r\n        })\r\n    },\r\n    resetWeights() {\r\n      this.resetWeightsAction()\r\n      this.$message.info('已重置权重为默认值，点击「确认」按钮生效')\r\n    },\r\n    handleScroll({ scrollTop, scrollHeight, clientHeight }) {\r\n      this.isBottom = scrollTop + clientHeight >= scrollHeight - 5\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* Element UI 表格样式覆盖 */\r\n.el-table {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.scroll-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 200px); /* 根据实际情况调整 */\r\n  min-height: 380px;\r\n}\r\n\r\n/* 调整滚动条样式 */\r\n.custom-scrollbar {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px; /* 增加与底部提示的间距 */\r\n}\r\n\r\n/* 底部提示样式优化 */\r\n.bottom-notice {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  color: #909399;\r\n  background: #f5f7fa;\r\n  border-top: 1px solid #dfe6ec;\r\n  transition: all 0.3s;\r\n  opacity: 0;\r\n}\r\n\r\n.show-notice {\r\n  opacity: 1;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n/* 表格列样式微调 */\r\n.el-table-column {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 权重设置区域样式 */\r\n.weight-settings {\r\n  margin: 12px;\r\n  padding: 12px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.weight-settings h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.weight-item {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;  /* 关键：平均分配宽度 */\r\n  height: 32px;\r\n}\r\n\r\n/* 新增：确保滑块容器占满剩余空间 */\r\n.weight-item .el-slider {\r\n  flex: 1;\r\n  min-width: 120px;  /* 防止内容过窄 */\r\n}\r\n\r\n.weight-item span {\r\n  width: 70px;\r\n  text-align: right;\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 保持滑块细节样式 */\r\n.weight-item .el-slider__runway {\r\n  height: 4px !important;\r\n}\r\n.weight-item .el-slider__button {\r\n  width: 12px !important;\r\n  height: 12px !important;\r\n}\r\n</style>\r\n"]}]}