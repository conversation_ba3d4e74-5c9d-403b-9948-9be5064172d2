{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=style&index=0&id=09ac478a&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749131212448}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1731739006000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOS4iuS8oOWSjOmAieaLqeWuueWZqOagt+W8jyAqLw0KLnVwbG9hZC1hbmQtc2VsZWN0LWNvbnRhaW5lciB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLyog5LiK5Lyg5Yy65Z+f5qC35byPICovDQoudXBsb2FkLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoudXBsb2FkLWRlbW8gew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnVwbG9hZC1kZW1vIC5lbC11cGxvYWQtZHJhZ2dlciB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDE4MHB4Ow0KICBib3JkZXI6IDJweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuM3M7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkLWRyYWdnZXI6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkLWRyYWdnZXIgLmVsLWljb24tdXBsb2FkIHsNCiAgZm9udC1zaXplOiA2N3B4Ow0KICBjb2xvcjogI2MwYzRjYzsNCiAgbWFyZ2luOiA0MHB4IDAgMTZweDsNCiAgbGluZS1oZWlnaHQ6IDUwcHg7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkX190ZXh0IHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoudXBsb2FkLWRlbW8gLmVsLXVwbG9hZF9fdGV4dCBlbSB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXN0eWxlOiBub3JtYWw7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkX190aXAgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tdG9wOiA3cHg7DQp9DQoNCi51cGxvYWQtYnV0dG9ucyB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTJweDsNCn0NCg0KLnNlbGVjdGlvbi1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnNlY3Rpb24taGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnNlY3Rpb24taGVhZGVyIGgzIHsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5zZWN0aW9uLWRlc2Mgew0KICBtYXJnaW46IDA7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi8qIOaWh+S7tuWIl+ihqOWuueWZqCAqLw0KLmZpbGUtbGlzdC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouZmlsZS10YWJsZS13cmFwcGVyIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBtYXgtaGVpZ2h0OiA0MDBweDsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQoNCi8qIOiHquWumuS5ieihqOagvOa7muWKqOadoeagt+W8jyAqLw0KLmZpbGUtdGFibGUtd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogOHB4Ow0KICBoZWlnaHQ6IDhweDsNCn0NCg0KLmZpbGUtdGFibGUtd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiAjZjFmMWYxOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5maWxlLXRhYmxlLXdyYXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogI2MwYzRjYzsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouZmlsZS10YWJsZS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICNhOGFlYjM7DQp9DQoNCi8qIOW3sumAieaLqeaVsOaNruihqOWMuuWfnyAqLw0KLnNlbGVjdGVkLXRhYmxlcy1zZWN0aW9uIHsNCiAgbWFyZ2luOiAyMHB4IDA7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQogIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCn0NCg0KLnNlbGVjdGVkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi5zZWxlY3RlZC10YWJsZXMtbGlzdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi8qIOaTjeS9nOaMiemSruWMuuWfnyAqLw0KLmFjdGlvbi1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouYWN0aW9uLWJ1dHRvbnMgLmVsLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDEycHggMjBweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQovKiDov5vluqbmmL7npLrljLrln58gKi8NCi5wcm9ncmVzcy1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgcGFkZGluZzogMTVweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLnByb2dyZXNzLXRleHQgew0KICBtYXJnaW46IDEwcHggMCAwIDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLyog5Y2h54mH5qC35byPICovDQouYm94LWNhcmQgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouZWwtdGFibGUgew0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KfQ0KDQovKiDmu5rliqjlrrnlmaggKi8NCi5jdXN0b20tc2Nyb2xsYmFyIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBvdmVyZmxvdzogYXV0bzsNCiAgcGFkZGluZy1yaWdodDogMTJweDsNCn0NCg0KLyog5Z6C55u05rua5Yqo5p2hICovDQouY3VzdG9tLXNjcm9sbGJhcjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogOHB4OyAvKiDlnoLnm7Tmu5rliqjmnaHlrr3luqYgKi8NCn0NCg0KLyog5rC05bmz5rua5Yqo5p2hICovDQouY3VzdG9tLXNjcm9sbGJhcjo6LXdlYmtpdC1zY3JvbGxiYXI6aG9yaXpvbnRhbCB7DQogIGhlaWdodDogOHB4OyAvKiDmsLTlubPmu5rliqjmnaHpq5jluqYgKi8NCiAgbWFyZ2luLWJvdHRvbTogMHB4OzsNCn0NCg0KLyog5rua5Yqo5p2h6L2o6YGTICovDQouY3VzdG9tLXNjcm9sbGJhcjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiAjZjFmMWYxOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi8qIOa7muWKqOadoea7keWdlyAqLw0KLmN1c3RvbS1zY3JvbGxiYXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogI2MwYzRjYzsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQovKiDmu5rliqjmnaHmu5HlnZfmgqzlgZzmlYjmnpwgKi8NCi5jdXN0b20tc2Nyb2xsYmFyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICNhOGFlYjM7DQp9DQovKiDmu5rliqjlrrnlmaggKi8NCi8qIOihqOagvOagt+W8j+S8mOWMliAqLw0KLmZpbGUtbGlzdC1jb250YWluZXIgLmVsLXRhYmxlIHRoIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5maWxlLWxpc3QtY29udGFpbmVyIC5lbC10YWJsZSB0ZCB7DQogIHBhZGRpbmc6IDEycHggMDsNCn0NCg0KLmZpbGUtbGlzdC1jb250YWluZXIgLmVsLXRhYmxlIC5lbC1pY29uLWRvY3VtZW50IHsNCiAgY29sb3I6ICM2N2MyM2E7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLyog6KGo5qC86KGM5oKs5YGc5pWI5p6cICovDQouZmlsZS1saXN0LWNvbnRhaW5lciAuZWwtdGFibGUgdGJvZHkgdHI6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQovKiDorrDlvZXmlbDmoLflvI8gKi8NCi5maWxlLWxpc3QtY29udGFpbmVyIC5lbC10YWJsZSAucmVjb3JkLWNvdW50IHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi8qIOeKtuaAgeagh+etvuagt+W8j+iwg+aVtCAqLw0KLmZpbGUtbGlzdC1jb250YWluZXIgLmVsLXRhZyB7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQouc2Nyb2xsLWNvbnRhaW5lciB7DQogIGhlaWdodDogNjAwcHg7IC8qIOWbuuWumumrmOW6piAqLw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi8qIOihqOagvOmrmOW6puiHqumAguW6lOWuueWZqCAqLw0KLmVsLXRhYmxlIHsNCiAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7DQp9DQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuYWN0aW9uLWJ1dHRvbnMgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIH0NCg0KICAuYWN0aW9uLWJ1dHRvbnMgLmVsLWJ1dHRvbiB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6hBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到服务器</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器中选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"exceptionType\" label=\"异常类型\" width=\"150\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"getExceptionTypeColor(row.exceptionType)\" size=\"small\">\r\n{{ row.exceptionType }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // 数据表选择相关\r\n      availableTables: [\r\n        // 添加一些测试数据，以防后端没有数据时也能看到表格\r\n        {\r\n          id: 1,\r\n          tableName: 'test_table_1',\r\n          createDate: '2024-12-20 10:00:00',\r\n          recordCount: 1000,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          tableName: 'test_table_2',\r\n          createDate: '2024-12-20 11:00:00',\r\n          recordCount: 2000,\r\n          status: 'available'\r\n        }\r\n      ], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 这里将来连接后端API上传文件\r\n        // const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n        //   headers: {\r\n        //     'Content-Type': 'multipart/form-data'\r\n        //   },\r\n        //   timeout: 60000\r\n        // })\r\n\r\n        // 模拟上传时间\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 模拟上传成功，添加到可用数据表列表\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          const newTable = {\r\n            id: this.availableTables.length + index + 1,\r\n            tableName: fileItem.name.replace(/\\.(xlsx|xls)$/i, ''), // 移除文件扩展名作为表名\r\n            createDate: new Date().toLocaleString('zh-CN'),\r\n            recordCount: Math.floor(Math.random() * 5000) + 100, // 模拟记录数\r\n            status: 'available'\r\n          }\r\n          this.availableTables.push(newTable)\r\n        })\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 根据后端返回的数据结构转换为前端显示格式\r\n                const exception = {\r\n                  orderNo: item['订单号'] || `异常-${Date.now()}-${index}`,\r\n                  category: exceptionType, // 异常类型作为分类\r\n                  specs: `${exceptionType}异常`,\r\n                  unitPrice: 0,\r\n                  quantity: 1,\r\n                  totalAmount: 0,\r\n                  payerName: item['支付人姓名'] || '未知',\r\n                  idNumber: item['支付人身份证号'] || '未知',\r\n                  phone: '未提供',\r\n                  orderDate: new Date().toISOString().split('T')[0],\r\n                  orderTime: new Date().toTimeString().split(' ')[0],\r\n                  paymentDate: new Date().toISOString().split('T')[0],\r\n                  paymentTime: new Date().toTimeString().split(' ')[0],\r\n                  logisticsNo: item['物流单号'] || '未知',\r\n                  exceptionType: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}