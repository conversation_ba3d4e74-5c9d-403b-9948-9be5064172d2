<template>
<div class="app-container">
<div class="upload-and-select-container">
<!-- 文件上传区域 -->
<div class="upload-section">
<div class="section-header">
<h3>文件上传</h3>
<p class="section-desc">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>
</div>
<el-upload
ref="upload"
class="upload-demo"
action=""
:on-change="handleFileChange"
:on-remove="handleFileRemove"
:before-upload="beforeUpload"
:auto-upload="false"
:file-list="uploadFileList"
multiple
accept=".xlsx,.xls"
drag
>
<i class="el-icon-upload"></i>
<div class="el-upload__text">将Excel文件拖到此处，或<em>点击选择文件</em></div>
<div class="el-upload__tip" slot="tip">支持选择多个Excel文件(.xlsx, .xls格式)</div>
</el-upload>
<div class="upload-buttons">
<el-button
type="primary"
icon="el-icon-upload2"
:loading="uploading"
:disabled="uploadFileList.length === 0"
@click="handleUpload"
>
{{ uploading ? '上传中...' : '上传文件' }}
</el-button>
<el-button
icon="el-icon-delete"
:disabled="uploadFileList.length === 0"
@click="clearUploadFiles"
>
清空文件
</el-button>
</div>
</div>

<!-- Excel文件选择区域 -->
<div class="selection-section">
<div class="section-header">
<h3>选择Excel文件进行异常检测</h3>
<p class="section-desc">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>
</div>

<!-- 文件列表展示 -->
<div class="file-list-container">
<div class="file-table-wrapper">
<el-table
ref="tableList"
:data="availableTables"
border
fit
highlight-current-row
style="width: 100%"
height="400"
@selection-change="handleSelectionChange"
>
<el-table-column
type="selection"
width="55"
align="center"
/>
<el-table-column prop="tableName" label="文件名" min-width="250">
<template #default="{row}">
<i class="el-icon-s-grid" />
<span style="margin-left: 8px;">{{ row.tableName }}</span>
</template>
</el-table-column>
<el-table-column prop="createDate" label="创建时间" width="180" align="center" />
<el-table-column prop="recordCount" label="记录数" width="120" align="center">
<template #default="{row}">
<span class="record-count">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>
</template>
</el-table-column>
<el-table-column label="状态" width="100" align="center">
<template #default="{row}">
<el-tag :type="row.status === 'available' ? 'success' : 'info'" size="small">
{{ row.status === 'available' ? '可用' : '处理中' }}
</el-tag>
</template>
</el-table-column>
</el-table>
</div>
</div>
</div>

<!-- 已选择Excel文件显示 -->
<div v-if="selectedTables.length > 0" class="selected-tables-section">
<div class="selected-header">
<span>已选择 {{ selectedTables.length }} 个Excel文件</span>
<el-button type="text" @click="clearSelection">清空选择</el-button>
</div>
<div class="selected-tables-list">
<el-tag
v-for="table in selectedTables"
:key="table.id"
closable
style="margin: 4px;"
@close="removeSelectedTable(table)"
>
{{ table.tableName }}
</el-tag>
</div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
<el-button
type="primary"
icon="el-icon-refresh"
:loading="loadingFiles"
@click="loadAvailableFiles"
>
刷新Excel文件列表
</el-button>
<el-button
type="success"
icon="el-icon-s-data"
:loading="processing"
:disabled="selectedTables.length === 0"
@click="processSelectedTables"
>
{{ processing ? '处理中...' : '异常检测分析' }}
</el-button>
<el-button
icon="el-icon-delete"
:disabled="selectedTables.length === 0"
@click="clearSelection"
>
清空选择
</el-button>
</div>

<!-- 进度显示 -->
<div v-if="uploading || processing" class="progress-section">
<el-progress
:percentage="uploading ? uploadProgress : processProgress"
:status="(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''"
:stroke-width="8"
/>
<p class="progress-text">{{ uploading ? uploadProgressText : progressText }}</p>
</div>
</div>

<el-card class="box-card">
<div slot="header" class="clearfix">
<span>异常物流订单列表</span>
</div>
<div class="scroll-container">
<div ref="scrollContainer" class="custom-scrollbar" @scroll="handleScroll">
<el-table
:data="exceptionList"
border
fit
highlight-current-row
style="width: 100%; height: 100%"
>
<el-table-column prop="orderNo" label="订单号" width="180" align="center" />
<el-table-column prop="exceptionType" label="异常类型" width="150" align="center">
<template #default="{row}">
<el-tag :type="getExceptionTypeColor(row.exceptionType)" size="small">
{{ row.exceptionType }}
</el-tag>
</template>
</el-table-column>
<el-table-column prop="specs" label="商品规格" width="180" />
<el-table-column prop="unitPrice" label="单价" align="right" width="110">
<template #default="{row}">
¥{{ row.unitPrice.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="quantity" label="数量" width="80" align="center" />
<el-table-column prop="totalAmount" label="订单金额" align="right" width="130">
<template #default="{row}">
¥{{ row.totalAmount.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="payerName" label="支付人" width="120" />
<el-table-column prop="idNumber" label="身份证号" width="180" />
<el-table-column prop="phone" label="联系电话" width="130" />
<el-table-column prop="orderDate" label="下单日期" width="120" />
<el-table-column prop="paymentDate" label="支付日期" width="120" />
<el-table-column prop="logisticsNo" label="物流单号" width="180" />
</el-table>
</div>
</div>
</el-card>
</div>

</template>

<script>
import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      // 文件上传相关
      uploadFileList: [],
      uploading: false,
      uploadProgress: 0,
      uploadProgressText: '',

      // Excel文件选择相关
      availableTables: [], // 从后端动态加载
      selectedTables: [],
      loadingFiles: false,
      processing: false,
      processProgress: 0,
      progressText: '',

      // 异常数据列表
      exceptionList: [], // 从后端异常检测获取
      scrollContainer: null
    }
  },
  mounted() {
    // 初始化时清空异常数据列表，等待用户选择文件
    this.exceptionList = []
    // 加载可用文件列表
    this.loadAvailableFiles()
  },
  methods: {
    // 文件上传相关方法
    handleFileChange(file, fileList) {
      this.uploadFileList = fileList
      console.log('上传文件列表更新:', fileList)
    },

    handleFileRemove(file, fileList) {
      this.uploadFileList = fileList
      console.log('文件已移除:', file.name)
    },

    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      return false // 阻止自动上传，手动控制
    },

    clearUploadFiles() {
      this.uploadFileList = []
      this.$refs.upload.clearFiles()
      this.$message.info('已清空上传文件列表')
    },

    async handleUpload() {
      if (this.uploadFileList.length === 0) {
        this.$message.warning('请先选择要上传的Excel文件')
        return
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadProgressText = '准备上传文件...'

      try {
        const formData = new FormData()

        // 添加所有文件到FormData
        this.uploadFileList.forEach((fileItem, index) => {
          formData.append('files', fileItem.raw)
        })

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (this.uploadProgress < 90) {
            this.uploadProgress += Math.random() * 10
            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`
          }
        }, 200)

        // 真正调用后端API上传文件
        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传
        try {
          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 60000
          })

          // 检查上传结果
          if (!response.data || !response.data.success) {
            throw new Error(response.data?.message || '上传失败')
          }
        } catch (uploadError) {
          // 如果上传接口不存在，使用模拟上传
          if (uploadError.response && uploadError.response.status === 404) {
            console.warn('上传接口不存在，使用模拟上传')
            await new Promise(resolve => setTimeout(resolve, 2000))
          } else {
            throw uploadError
          }
        }

        clearInterval(progressInterval)
        this.uploadProgress = 100
        this.uploadProgressText = '文件上传完成！'

        // 上传成功后，重新加载服务器上的Excel文件列表
        await this.loadAvailableFiles()

        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)
        this.clearUploadFiles()
      } catch (error) {
        console.error('上传失败:', error)
        this.uploadProgress = 0
        this.uploadProgressText = ''
        this.$message.error(`上传失败: ${error.message}`)
      } finally {
        this.uploading = false
        setTimeout(() => {
          this.uploadProgress = 0
          this.uploadProgressText = ''
        }, 3000)
      }
    },

    // 加载可用数据表列表
    async loadAvailableFiles() {
      this.loadingFiles = true
      try {
        // 调用后端API获取所有Excel文件路径
        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')
        console.log('后端返回的Excel文件路径:', response.data)
        console.log('这些文件来自path_default文件夹:', response.data.paths)

        if (response.data && response.data.paths) {
          // 将文件路径转换为前端显示格式
          this.availableTables = response.data.paths.map((filePath, index) => {
            // 提取文件名作为表名显示
            const fileName = filePath.split('\\').pop() || filePath.split('/').pop()
            const tableName = fileName.replace('.xlsx', '') // 移除扩展名

            return {
              id: index + 1,
              tableName: tableName, // 显示文件名（不含扩展名）
              filePath: filePath, // 保存完整路径用于后端处理
              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值
              recordCount: null, // 后端没有提供记录数
              status: 'available'
            }
          })
          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)
        } else {
          this.$message.warning('没有找到可用的Excel文件')
        }
      } catch (error) {
        console.error('加载Excel文件列表失败:', error)
        this.$message.error('加载Excel文件列表失败: ' + error.message)
      } finally {
        this.loadingFiles = false
      }
    },

    // 处理Excel文件选择变化
    handleSelectionChange(selection) {
      this.selectedTables = selection
      console.log('已选择Excel文件:', selection)
    },

    // 移除已选择的Excel文件
    removeSelectedTable(table) {
      const index = this.selectedTables.findIndex(t => t.id === table.id)
      if (index > -1) {
        this.selectedTables.splice(index, 1)
      }
      // 同时更新表格选择状态
      this.$nextTick(() => {
        const tableRef = this.$refs.tableList
        if (tableRef) {
          tableRef.toggleRowSelection(table, false)
        }
      })
    },

    // 清空选择
    clearSelection() {
      this.selectedTables = []
      // 清空表格选择
      this.$nextTick(() => {
        const tableRef = this.$refs.tableList
        if (tableRef) {
          tableRef.clearSelection()
        }
      })
      this.$message.info('已清空Excel文件选择')
    },
    async processSelectedTables() {
      if (this.selectedTables.length === 0) {
        this.$message.warning('请先选择要处理的Excel文件')
        return
      }

      this.processing = true
      this.processProgress = 0
      this.progressText = '开始处理Excel文件...'

      try {
        // 进度更新
        const progressInterval = setInterval(() => {
          if (this.processProgress < 80) {
            this.processProgress += Math.random() * 10
            const currentStep = Math.floor(this.processProgress / 25)
            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']
            this.progressText = steps[currentStep] || '处理中...'
          }
        }, 500)

        // 调用后端异常检测接口
        const filePaths = this.selectedTables.map(t => t.filePath)
        console.log('选中的表格数据:', this.selectedTables)
        console.log('发送到后端的文件路径:', filePaths)
        console.log('这些路径来自path_default文件夹:', filePaths)

        this.progressText = '正在调用后端分析接口...'

        // 真正调用后端API
        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {
          filenames: filePaths
        })

        clearInterval(progressInterval)
        this.processProgress = 100
        this.progressText = '数据处理完成！'

        console.log('后端返回的异常检测结果:', response.data)

        // 处理后端返回的异常数据
        if (response.data) {
          const exceptionList = []

          // 遍历后端返回的各种异常类型
          Object.keys(response.data).forEach(exceptionType => {
            const exceptions = response.data[exceptionType]
            if (exceptions && exceptions.length > 0) {
              exceptions.forEach((item, index) => {
                // 根据后端返回的数据结构转换为前端显示格式
                const exception = {
                  orderNo: item['订单号'] || `异常-${Date.now()}-${index}`,
                  category: exceptionType, // 异常类型作为分类
                  specs: `${exceptionType}异常`,
                  unitPrice: 0,
                  quantity: 1,
                  totalAmount: 0,
                  payerName: item['支付人姓名'] || '未知',
                  idNumber: item['支付人身份证号'] || '未知',
                  phone: '未提供',
                  orderDate: new Date().toISOString().split('T')[0],
                  orderTime: new Date().toTimeString().split(' ')[0],
                  paymentDate: new Date().toISOString().split('T')[0],
                  paymentTime: new Date().toTimeString().split(' ')[0],
                  logisticsNo: item['物流单号'] || '未知',
                  exceptionType: exceptionType // 添加异常类型字段
                }
                exceptionList.push(exception)
              })
            }
          })

          this.exceptionList = exceptionList

          if (exceptionList.length > 0) {
            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)
          } else {
            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)
          }
        } else {
          this.$message.warning('后端返回数据格式异常')
        }
      } catch (error) {
        console.error('处理失败:', error)
        this.processProgress = 0
        this.progressText = ''

        if (error.response) {
          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)
        } else if (error.request) {
          this.$message.error('网络连接失败，请检查后端服务是否启动')
        } else {
          this.$message.error(`处理失败: ${error.message}`)
        }
      } finally {
        this.processing = false
        setTimeout(() => {
          this.processProgress = 0
          this.progressText = ''
        }, 3000)
      }
    },

    handleScroll(event) {
      // 处理滚动事件
      console.log('Scrolling...', event)
    },

    // 根据异常类型返回对应的标签颜色
    getExceptionTypeColor(exceptionType) {
      const colorMap = {
        '同一姓名多个身份证': 'danger',
        '同一身份证多个姓名': 'warning',
        '物流单号重复': 'info',
        '订单号多个身份证': 'success'
      }
      return colorMap[exceptionType] || 'primary'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 上传和选择容器样式 */
.upload-and-select-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 上传区域样式 */
.upload-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.upload-demo {
  width: 100%;
}

.upload-demo .el-upload-dragger {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-demo .el-upload-dragger:hover {
  border-color: #409eff;
}

.upload-demo .el-upload-dragger .el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}

.upload-demo .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-demo .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

.upload-demo .el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

.upload-buttons {
  margin-top: 15px;
  display: flex;
  gap: 12px;
}

.selection-section {
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.section-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 文件列表容器 */
.file-list-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.file-table-wrapper {
  position: relative;
  max-height: 400px;
  overflow: auto;
}

/* 自定义表格滚动条样式 */
.file-table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.file-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 已选择数据表区域 */
.selected-tables-section {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 600;
  color: #409eff;
}

.selected-tables-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.action-buttons .el-button {
  padding: 12px 20px;
  font-size: 14px;
}

/* 进度显示区域 */
.progress-section {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.progress-text {
  margin: 10px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* 卡片样式 */
.box-card {
  margin-top: 20px;
}

.el-table {
  margin-top: 15px;
}

/* 滚动容器 */
.custom-scrollbar {
  height: 100%;
  overflow: auto;
  padding-right: 12px;
}

/* 垂直滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
}

/* 水平滚动条 */
.custom-scrollbar::-webkit-scrollbar:horizontal {
  height: 8px; /* 水平滚动条高度 */
  margin-bottom: 0px;;
}

/* 滚动条轨道 */
.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

/* 滚动条滑块 */
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

/* 滚动条滑块悬停效果 */
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}
/* 滚动容器 */
/* 表格样式优化 */
.file-list-container .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.file-list-container .el-table td {
  padding: 12px 0;
}

.file-list-container .el-table .el-icon-document {
  color: #67c23a;
  font-size: 16px;
}

/* 表格行悬停效果 */
.file-list-container .el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 记录数样式 */
.file-list-container .el-table .record-count {
  font-weight: 600;
  color: #409eff;
}

/* 状态标签样式调整 */
.file-list-container .el-tag {
  font-weight: 500;
}
.scroll-container {
  height: 600px; /* 固定高度 */
  position: relative;
}

/* 表格高度自适应容器 */
.el-table {
  height: 100% !important;
}
/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
