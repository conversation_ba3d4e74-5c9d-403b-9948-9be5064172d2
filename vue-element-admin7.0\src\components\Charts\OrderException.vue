<template>
<div class="app-container">
<div class="upload-container">
<!-- 文件上传区域 -->
<div class="upload-section">
<el-upload
ref="upload"
class="upload-demo"
:action="uploadUrl"
:on-change="handleFileChange"
:on-remove="handleFileRemove"
:before-upload="beforeUpload"
:auto-upload="false"
:file-list="fileList"
multiple
accept=".xlsx,.xls"
drag
>
<i class="el-icon-upload"></i>
<div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
<div class="el-upload__tip" slot="tip">支持选择多个Excel文件(.xlsx, .xls格式)</div>
</el-upload>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
<el-button
type="primary"
icon="el-icon-upload2"
:loading="uploading"
:disabled="fileList.length === 0"
@click="handleUpload"
>
{{ uploading ? '处理中...' : '导入数据' }}
</el-button>
<el-button
icon="el-icon-delete"
:disabled="fileList.length === 0"
@click="clearFiles"
>
清空文件
</el-button>
</div>

<!-- 进度显示 -->
<div v-if="uploading" class="progress-section">
<el-progress
:percentage="uploadProgress"
:status="uploadProgress === 100 ? 'success' : ''"
:stroke-width="8"
>
</el-progress>
<p class="progress-text">{{ progressText }}</p>
</div>
</div>

<el-card class="box-card">
<div slot="header" class="clearfix">
<span>异常物流订单列表</span>
</div>
<div class="scroll-container">
<div ref="scrollContainer" class="custom-scrollbar" @scroll="handleScroll">
<el-table
:data="exceptionList"
border
fit
highlight-current-row
style="width: 100%; height: 100%"
>
<el-table-column prop="orderNo" label="订单号" width="180" align="center" />
<el-table-column prop="category" label="商品品类" width="120" />
<el-table-column prop="specs" label="商品规格" width="180" />
<el-table-column prop="unitPrice" label="单价" align="right" width="110">
<template #default="{row}">
¥{{ row.unitPrice.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="quantity" label="数量" width="80" align="center" />
<el-table-column prop="totalAmount" label="订单金额" align="right" width="130">
<template #default="{row}">
¥{{ row.totalAmount.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="payerName" label="支付人" width="120" />
<el-table-column prop="idNumber" label="身份证号" width="180" />
<el-table-column prop="phone" label="联系电话" width="130" />
<el-table-column prop="orderDate" label="下单日期" width="120" />
<el-table-column prop="orderTime" label="下单时间" width="100" />
<el-table-column prop="paymentDate" label="支付日期" width="120" />
<el-table-column prop="paymentTime" label="支付时间" width="100" />
<el-table-column prop="logisticsNo" label="物流单号" width="180" />
</el-table>
</div>
</div>
</el-card>
</div>

</template>

<script>
import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      // 文件上传相关
      fileList: [],
      uploading: false,
      uploadProgress: 0,
      progressText: '',
      uploadUrl: 'http://127.0.0.1:8000/upload-excel', // 后端上传接口

      // 异常数据列表
      exceptionList: [
        {
          orderNo: 'DD20240715001',
          category: '电子产品',
          specs: '笔记本电脑/16GB 512GB',
          unitPrice: 8999.00,
          quantity: 1,
          totalAmount: 8999.00,
          payerName: '李四',
          idNumber: '310***********5678',
          phone: '13900139000',
          orderDate: '2024-07-15',
          orderTime: '10:15',
          paymentDate: '2024-07-15',
          paymentTime: '10:20',
          logisticsNo: 'WL987654321'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        }
      ],
      scrollContainer: null
    }
  },
  mounted() {
    // 初始化时清空异常数据列表，等待用户上传文件
    this.exceptionList = []
  },
  methods: {
    // 文件选择变化处理
    handleFileChange(file, fileList) {
      this.fileList = fileList
      console.log('文件列表更新:', fileList)
    },

    // 文件移除处理
    handleFileRemove(file, fileList) {
      this.fileList = fileList
      console.log('文件已移除:', file.name)
    },

    // 上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      return false // 阻止自动上传，手动控制
    },

    // 清空文件列表
    clearFiles() {
      this.fileList = []
      this.$refs.upload.clearFiles()
      this.$message.info('已清空文件列表')
    },

    // 处理文件上传
    async handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的Excel文件')
        return
      }

      this.uploading = true
      this.uploadProgress = 0
      this.progressText = '准备上传文件...'

      try {
        const formData = new FormData()

        // 添加所有文件到FormData
        this.fileList.forEach((fileItem, index) => {
          formData.append('files', fileItem.raw)
        })

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (this.uploadProgress < 90) {
            this.uploadProgress += Math.random() * 10
            this.progressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`
          }
        }, 200)

        // 发送请求到后端
        const response = await axios.post(this.uploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          timeout: 60000 // 60秒超时
        })

        clearInterval(progressInterval)
        this.uploadProgress = 100
        this.progressText = '数据处理完成！'

        // 处理响应数据
        if (response.data && response.data.success) {
          this.exceptionList = response.data.exceptions || []
          this.$message.success(`成功处理 ${this.fileList.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)
        } else {
          throw new Error(response.data.message || '数据处理失败')
        }

      } catch (error) {
        console.error('上传失败:', error)
        this.uploadProgress = 0
        this.progressText = ''

        if (error.code === 'ECONNABORTED') {
          this.$message.error('请求超时，请检查网络连接或文件大小')
        } else if (error.response) {
          this.$message.error(`上传失败: ${error.response.data.message || error.message}`)
        } else {
          this.$message.error(`上传失败: ${error.message}`)
        }
      } finally {
        this.uploading = false
        setTimeout(() => {
          this.uploadProgress = 0
          this.progressText = ''
        }, 3000)
      }
    },

    handleScroll(event) {
      // 处理滚动事件
      console.log('Scrolling...', event)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 上传容器样式 */
.upload-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-demo {
  width: 100%;
}

/* 上传区域样式优化 */
.upload-demo .el-upload-dragger {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-demo .el-upload-dragger:hover {
  border-color: #409eff;
}

.upload-demo .el-upload-dragger .el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}

.upload-demo .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-demo .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

.upload-demo .el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.action-buttons .el-button {
  padding: 12px 20px;
  font-size: 14px;
}

/* 进度显示区域 */
.progress-section {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.progress-text {
  margin: 10px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* 卡片样式 */
.box-card {
  margin-top: 20px;
}

.el-table {
  margin-top: 15px;
}

/* 滚动容器 */
.scroll-container {
  height: 600px;
  position: relative;
}

.custom-scrollbar {
  height: 100%;
  overflow: auto;
  padding-right: 12px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 文件列表样式优化 */
.upload-demo .el-upload-list {
  margin-top: 15px;
}

.upload-demo .el-upload-list__item {
  padding: 8px 10px;
  margin-top: 5px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
