{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749127241460}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzDQogICAgICB1cGxvYWRGaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsDQogICAgICB1cGxvYWRQcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDmlofku7bpgInmi6nnm7jlhbMNCiAgICAgIGF2YWlsYWJsZUZpbGVzOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICBmaWxlTmFtZTogJ+iuouWNleaVsOaNrl8yMDI0UTEueGxzeCcsDQogICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDEtMTUgMTA6MzA6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAxMjUwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIGZpbGVOYW1lOiAn54mp5rWB5L+h5oGvXzIwMjRRMS54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wMS0yMCAxNDoyMDowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDk4MCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICBmaWxlTmFtZTogJ+iuouWNleaVsOaNrl8yMDI0UTIueGxzeCcsDQogICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDQtMTAgMDk6MTU6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAxNjgwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIGZpbGVOYW1lOiAn54mp5rWB5L+h5oGvXzIwMjRRMi54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wNC0xNSAxNjo0NTowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDE0MjAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDUsDQogICAgICAgICAgZmlsZU5hbWU6ICforqLljZXmlbDmja5fMjAyNFEzLnhsc3gnLA0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTA3LTA4IDExOjMwOjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogMjEwMCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNiwNCiAgICAgICAgICBmaWxlTmFtZTogJ+eJqea1geS/oeaBr18yMDI0UTMueGxzeCcsDQogICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDctMTIgMTU6MjA6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAxODkwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA3LA0KICAgICAgICAgIGZpbGVOYW1lOiAn6K6i5Y2V5pWw5o2uXzIwMjRRNC54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0xMC0wNSAwOTo0NTowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDIzNTAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDgsDQogICAgICAgICAgZmlsZU5hbWU6ICfnianmtYHkv6Hmga9fMjAyNFE0Lnhsc3gnLA0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTEwLTA4IDE0OjMwOjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogMjE4MCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogOSwNCiAgICAgICAgICBmaWxlTmFtZTogJ+WuouaIt+S/oeaBr18yMDI05bm05bqmLnhsc3gnLA0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTEyLTAxIDEwOjE1OjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogNTYwMCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMTAsDQogICAgICAgICAgZmlsZU5hbWU6ICfkvpvlupTllYbmlbDmja5fMjAyNOW5tOW6pi54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0xMi0wMiAxMToyMDowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDg5MCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMTEsDQogICAgICAgICAgZmlsZU5hbWU6ICfpgIDotKforrDlvZVfMjAyNFExLVEyLnhsc3gnLA0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTA2LTMwIDE2OjQ1OjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogMzIwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxMiwNCiAgICAgICAgICBmaWxlTmFtZTogJ+mAgOi0p+iusOW9lV8yMDI0UTMtUTQueGxzeCcsDQogICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMTItMTUgMDk6MzA6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAyODAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEzLA0KICAgICAgICAgIGZpbGVOYW1lOiAn5byC5bi46K6i5Y2VX+WOhuWPsuaVsOaNri54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0xMS0yMCAxMzoxNTowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDE1NiwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMTQsDQogICAgICAgICAgZmlsZU5hbWU6ICfnianmtYHot5/ouKpf6K+m57uG6K6w5b2VLnhsc3gnLA0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTEyLTEwIDA4OjQ1OjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogNDIwMCwNCiAgICAgICAgICBzdGF0dXM6ICdwcm9jZXNzaW5nJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDE1LA0KICAgICAgICAgIGZpbGVOYW1lOiAn6K6i5Y2V57uf6K6hX+aciOW6puaxh+aAuy54bHN4JywNCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0xMi0xOCAxNDoyMDowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDcyMCwNCiAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBzZWxlY3RlZEZpbGVzOiBbXSwNCiAgICAgIGxvYWRpbmdGaWxlczogZmFsc2UsDQogICAgICBwcm9jZXNzaW5nOiBmYWxzZSwNCiAgICAgIHByb2Nlc3NQcm9ncmVzczogMCwNCiAgICAgIHByb2dyZXNzVGV4dDogJycsDQoNCiAgICAgIC8vIOW8guW4uOaVsOaNruWIl+ihqA0KICAgICAgZXhjZXB0aW9uTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDEnLA0KICAgICAgICAgIGNhdGVnb3J5OiAn55S15a2Q5Lqn5ZOBJywNCiAgICAgICAgICBzcGVjczogJ+eslOiusOacrOeUteiEkS8xNkdCIDUxMkdCJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5OTkuMDAsDQogICAgICAgICAgcXVhbnRpdHk6IDEsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDg5OTkuMDAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn5p2O5ZubJywNCiAgICAgICAgICBpZE51bWJlcjogJzMxMCoqKioqKioqKioqNTY3OCcsDQogICAgICAgICAgcGhvbmU6ICcxMzkwMDEzOTAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNScsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTA6MTUnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNScsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxMDoyMCcsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDk4NzY1NDMyMScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHNjcm9sbENvbnRhaW5lcjogbnVsbA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJbml7bmuIXnqbrlvILluLjmlbDmja7liJfooajvvIznrYnlvoXnlKjmiLfpgInmi6nmlofku7YNCiAgICB0aGlzLmV4Y2VwdGlvbkxpc3QgPSBbXQ0KICAgIC8vIOWKoOi9veWPr+eUqOaWh+S7tuWIl+ihqA0KICAgIHRoaXMubG9hZEF2YWlsYWJsZUZpbGVzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaWh+S7tuS4iuS8oOebuOWFs+aWueazlQ0KICAgIGhhbmRsZUZpbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QgPSBmaWxlTGlzdA0KICAgICAgY29uc29sZS5sb2coJ+S4iuS8oOaWh+S7tuWIl+ihqOabtOaWsDonLCBmaWxlTGlzdCkNCiAgICB9LA0KDQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IGZpbGVMaXN0DQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25bey56e76ZmkOicsIGZpbGUubmFtZSkNCiAgICB9LA0KDQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwNCiAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwDQoNCiAgICAgIGlmICghaXNFeGNlbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDEwTSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4cxME1CIScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlIC8vIOmYu+atouiHquWKqOS4iuS8oO+8jOaJi+WKqOaOp+WItg0KICAgIH0sDQoNCiAgICBjbGVhclVwbG9hZEZpbGVzKCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCkNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF56m65LiK5Lyg5paH5Lu25YiX6KGoJykNCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlVXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMudXBsb2FkRmlsZUxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5LiK5Lyg55qERXhjZWzmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMA0KICAgICAgdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAn5YeG5aSH5LiK5Lyg5paH5Lu2Li4uJw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQoNCiAgICAgICAgLy8g5re75Yqg5omA5pyJ5paH5Lu25YiwRm9ybURhdGENCiAgICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdC5mb3JFYWNoKChmaWxlSXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGVzJywgZmlsZUl0ZW0ucmF3KQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnVwbG9hZFByb2dyZXNzIDwgOTApIHsNCiAgICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgKz0gTWF0aC5yYW5kb20oKSAqIDEwDQogICAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9IGDmraPlnKjkuIrkvKDmlofku7YuLi4gJHtNYXRoLnJvdW5kKHRoaXMudXBsb2FkUHJvZ3Jlc3MpfSVgDQogICAgICAgICAgfQ0KICAgICAgICB9LCAyMDApDQoNCiAgICAgICAgLy8g6L+Z6YeM5bCG5p2l6L+e5o6l5ZCO56uvQVBJ5LiK5Lyg5paH5Lu2DQogICAgICAgIC8vIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL3VwbG9hZC1maWxlcycsIGZvcm1EYXRhLCB7DQogICAgICAgIC8vICAgaGVhZGVyczogew0KICAgICAgICAvLyAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJw0KICAgICAgICAvLyAgIH0sDQogICAgICAgIC8vICAgdGltZW91dDogNjAwMDANCiAgICAgICAgLy8gfSkNCg0KICAgICAgICAvLyDmqKHmi5/kuIrkvKDml7bpl7QNCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKQ0KDQogICAgICAgIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCkNCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDEwMA0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICfmlofku7bkuIrkvKDlrozmiJDvvIEnDQoNCiAgICAgICAgLy8g5qih5ouf5LiK5Lyg5oiQ5Yqf77yM5re75Yqg5Yiw5Y+v55So5paH5Lu25YiX6KGoDQogICAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QuZm9yRWFjaCgoZmlsZUl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgY29uc3QgbmV3RmlsZSA9IHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmF2YWlsYWJsZUZpbGVzLmxlbmd0aCArIGluZGV4ICsgMSwNCiAgICAgICAgICAgIGZpbGVOYW1lOiBmaWxlSXRlbS5uYW1lLA0KICAgICAgICAgICAgdXBsb2FkRGF0ZTogbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnemgtQ04nKSwNCiAgICAgICAgICAgIHJlY29yZENvdW50OiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1MDAwKSArIDEwMCwgLy8g5qih5ouf6K6w5b2V5pWwDQogICAgICAgICAgICBzdGF0dXM6ICdhdmFpbGFibGUnDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRmlsZXMucHVzaChuZXdGaWxlKQ0KICAgICAgICB9KQ0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5LiK5LygICR7dGhpcy51cGxvYWRGaWxlTGlzdC5sZW5ndGh9IOS4quaWh+S7tmApDQogICAgICAgIHRoaXMuY2xlYXJVcGxvYWRGaWxlcygpDQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4iuS8oOWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDANCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy51cGxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMA0KICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgfSwgMzAwMCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L295Y+v55So5paH5Lu25YiX6KGoDQogICAgYXN5bmMgbG9hZEF2YWlsYWJsZUZpbGVzKCkgew0KICAgICAgdGhpcy5sb2FkaW5nRmlsZXMgPSB0cnVlDQogICAgICB0cnkgew0KICAgICAgICAvLyDov5nph4zlsIbmnaXov57mjqXlkI7nq69BUEnojrflj5bmlofku7bliJfooagNCiAgICAgICAgLy8gY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoJ2h0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hdmFpbGFibGUtZmlsZXMnKQ0KICAgICAgICAvLyB0aGlzLmF2YWlsYWJsZUZpbGVzID0gcmVzcG9uc2UuZGF0YS5maWxlcyB8fCBbXQ0KDQogICAgICAgIC8vIOaooeaLn+WKoOi9veW7tui/nw0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSkNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7bliJfooajliqDovb3lrozmiJAnKQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295paH5Lu25YiX6KGo5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3mlofku7bliJfooajlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nRmlsZXMgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmlofku7bpgInmi6nlj5jljJYNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGVkRmlsZXMgPSBzZWxlY3Rpb24NCiAgICAgIGNvbnNvbGUubG9nKCflt7LpgInmi6nmlofku7Y6Jywgc2VsZWN0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTlt7LpgInmi6nnmoTmlofku7YNCiAgICByZW1vdmVTZWxlY3RlZEZpbGUoZmlsZSkgew0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLnNlbGVjdGVkRmlsZXMuZmluZEluZGV4KGYgPT4gZi5pZCA9PT0gZmlsZS5pZCkNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcy5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB9DQogICAgICAvLyDlkIzml7bmm7TmlrDooajmoLzpgInmi6nnirbmgIENCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLmZpbGVUYWJsZQ0KICAgICAgICBpZiAodGFibGUpIHsNCiAgICAgICAgICB0YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24oZmlsZSwgZmFsc2UpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOa4heepuumAieaLqQ0KICAgIGNsZWFyU2VsZWN0aW9uKCkgew0KICAgICAgdGhpcy5zZWxlY3RlZEZpbGVzID0gW10NCiAgICAgIC8vIOa4heepuuihqOagvOmAieaLqQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMuZmlsZVRhYmxlDQogICAgICAgIGlmICh0YWJsZSkgew0KICAgICAgICAgIHRhYmxlLmNsZWFyU2VsZWN0aW9uKCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF56m65paH5Lu26YCJ5oupJykNCiAgICB9LA0KICAgIGFzeW5jIHByb2Nlc3NTZWxlY3RlZEZpbGVzKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRGaWxlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHlpITnkIbnmoTmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5wcm9jZXNzaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICflvIDlp4vlpITnkIbmlofku7YuLi4nDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnByb2Nlc3NQcm9ncmVzcyA8IDkwKSB7DQogICAgICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTUNCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRTdGVwID0gTWF0aC5mbG9vcih0aGlzLnByb2Nlc3NQcm9ncmVzcyAvIDMwKQ0KICAgICAgICAgICAgY29uc3Qgc3RlcHMgPSBbJ+ato+WcqOivu+WPluaWh+S7ti4uLicsICfmraPlnKjlkIjlubbmlbDmja4uLi4nLCAn5q2j5Zyo5YiG5p6Q5byC5bi4Li4uJ10NCiAgICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gc3RlcHNbY3VycmVudFN0ZXBdIHx8ICflpITnkIbkuK0uLi4nDQogICAgICAgICAgfQ0KICAgICAgICB9LCAzMDApDQoNCiAgICAgICAgLy8g6LCD55So5ZCO56uv5byC5bi45qOA5rWL5o6l5Y+jDQogICAgICAgIGNvbnN0IGZpbGVuYW1lcyA9IHRoaXMuc2VsZWN0ZWRGaWxlcy5tYXAoZiA9PiBmLmZpbGVOYW1lKQ0KICAgICAgICBjb25zb2xlLmxvZygn5Y+R6YCB5Yiw5ZCO56uv55qE5paH5Lu25ZCNOicsIGZpbGVuYW1lcykNCg0KICAgICAgICAvLyBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoJ2h0dHA6Ly8xMjcuMC4wLjE6ODAwMC9nZXRfc3VzX1RyYWNraW5nTnVtJywgew0KICAgICAgICAvLyAgIGZpbGVuYW1lczogZmlsZW5hbWVzDQogICAgICAgIC8vIH0pDQoNCiAgICAgICAgLy8g5qih5ouf5aSE55CG5pe26Ze0DQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAzMDAwKSkNCg0KICAgICAgICBjbGVhckludGVydmFsKHByb2dyZXNzSW50ZXJ2YWwpDQogICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMTAwDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+aVsOaNruWkhOeQhuWujOaIkO+8gScNCg0KICAgICAgICAvLyDmqKHmi5/nlJ/miJDlvILluLjmlbDmja4NCiAgICAgICAgY29uc3QgbW9ja0V4Y2VwdGlvbnMgPSBbDQogICAgICAgICAgew0KICAgICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDEnLA0KICAgICAgICAgICAgY2F0ZWdvcnk6ICfnlLXlrZDkuqflk4EnLA0KICAgICAgICAgICAgc3BlY3M6ICfnrJTorrDmnKznlLXohJEvMTZHQiA1MTJHQicsDQogICAgICAgICAgICB1bml0UHJpY2U6IDg5OTkuMDAsDQogICAgICAgICAgICBxdWFudGl0eTogMSwNCiAgICAgICAgICAgIHRvdGFsQW1vdW50OiA4OTk5LjAwLA0KICAgICAgICAgICAgcGF5ZXJOYW1lOiAn5p2O5ZubJywNCiAgICAgICAgICAgIGlkTnVtYmVyOiAnMzEwKioqKioqKioqKio1Njc4JywNCiAgICAgICAgICAgIHBob25lOiAnMTM5MDAxMzkwMDAnLA0KICAgICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNScsDQogICAgICAgICAgICBvcmRlclRpbWU6ICcxMDoxNScsDQogICAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTUnLA0KICAgICAgICAgICAgcGF5bWVudFRpbWU6ICcxMDoyMCcsDQogICAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMOTg3NjU0MzIxJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KDQogICAgICAgIHRoaXMuZXhjZXB0aW9uTGlzdCA9IG1vY2tFeGNlcHRpb25zDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5aSE55CGICR7dGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aH0g5Liq5paH5Lu277yM5Y+R546wICR7dGhpcy5leGNlcHRpb25MaXN0Lmxlbmd0aH0g5p2h5byC5bi45pWw5o2uYCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WkhOeQhuWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5aSE55CG5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMucHJvY2Vzc2luZyA9IGZhbHNlDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMA0KICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgfSwgMzAwMCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlU2Nyb2xsKGV2ZW50KSB7DQogICAgICAvLyDlpITnkIbmu5rliqjkuovku7YNCiAgICAgIGNvbnNvbGUubG9nKCdTY3JvbGxpbmcuLi4nLCBldmVudCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到服务器</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择文件进行异常检测</h3>\r\n<p class=\"section-desc\">从已上传的文件中选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"200\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\" />\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"uploadDate\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount.toLocaleString() }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedFile(file)\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // 文件选择相关\r\n      availableFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: '订单数据_2024Q1.xlsx',\r\n          uploadDate: '2024-01-15 10:30:00',\r\n          recordCount: 1250,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: '物流信息_2024Q1.xlsx',\r\n          uploadDate: '2024-01-20 14:20:00',\r\n          recordCount: 980,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: '订单数据_2024Q2.xlsx',\r\n          uploadDate: '2024-04-10 09:15:00',\r\n          recordCount: 1680,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: '物流信息_2024Q2.xlsx',\r\n          uploadDate: '2024-04-15 16:45:00',\r\n          recordCount: 1420,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: '订单数据_2024Q3.xlsx',\r\n          uploadDate: '2024-07-08 11:30:00',\r\n          recordCount: 2100,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: '物流信息_2024Q3.xlsx',\r\n          uploadDate: '2024-07-12 15:20:00',\r\n          recordCount: 1890,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 7,\r\n          fileName: '订单数据_2024Q4.xlsx',\r\n          uploadDate: '2024-10-05 09:45:00',\r\n          recordCount: 2350,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 8,\r\n          fileName: '物流信息_2024Q4.xlsx',\r\n          uploadDate: '2024-10-08 14:30:00',\r\n          recordCount: 2180,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 9,\r\n          fileName: '客户信息_2024年度.xlsx',\r\n          uploadDate: '2024-12-01 10:15:00',\r\n          recordCount: 5600,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 10,\r\n          fileName: '供应商数据_2024年度.xlsx',\r\n          uploadDate: '2024-12-02 11:20:00',\r\n          recordCount: 890,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 11,\r\n          fileName: '退货记录_2024Q1-Q2.xlsx',\r\n          uploadDate: '2024-06-30 16:45:00',\r\n          recordCount: 320,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 12,\r\n          fileName: '退货记录_2024Q3-Q4.xlsx',\r\n          uploadDate: '2024-12-15 09:30:00',\r\n          recordCount: 280,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 13,\r\n          fileName: '异常订单_历史数据.xlsx',\r\n          uploadDate: '2024-11-20 13:15:00',\r\n          recordCount: 156,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 14,\r\n          fileName: '物流跟踪_详细记录.xlsx',\r\n          uploadDate: '2024-12-10 08:45:00',\r\n          recordCount: 4200,\r\n          status: 'processing'\r\n        },\r\n        {\r\n          id: 15,\r\n          fileName: '订单统计_月度汇总.xlsx',\r\n          uploadDate: '2024-12-18 14:20:00',\r\n          recordCount: 720,\r\n          status: 'available'\r\n        }\r\n      ],\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [\r\n        {\r\n          orderNo: 'DD20240715001',\r\n          category: '电子产品',\r\n          specs: '笔记本电脑/16GB 512GB',\r\n          unitPrice: 8999.00,\r\n          quantity: 1,\r\n          totalAmount: 8999.00,\r\n          payerName: '李四',\r\n          idNumber: '310***********5678',\r\n          phone: '13900139000',\r\n          orderDate: '2024-07-15',\r\n          orderTime: '10:15',\r\n          paymentDate: '2024-07-15',\r\n          paymentTime: '10:20',\r\n          logisticsNo: 'WL987654321'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        }\r\n      ],\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 这里将来连接后端API上传文件\r\n        // const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n        //   headers: {\r\n        //     'Content-Type': 'multipart/form-data'\r\n        //   },\r\n        //   timeout: 60000\r\n        // })\r\n\r\n        // 模拟上传时间\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 模拟上传成功，添加到可用文件列表\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          const newFile = {\r\n            id: this.availableFiles.length + index + 1,\r\n            fileName: fileItem.name,\r\n            uploadDate: new Date().toLocaleString('zh-CN'),\r\n            recordCount: Math.floor(Math.random() * 5000) + 100, // 模拟记录数\r\n            status: 'available'\r\n          }\r\n          this.availableFiles.push(newFile)\r\n        })\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 这里将来连接后端API获取文件列表\r\n        // const response = await axios.get('http://127.0.0.1:8000/available-files')\r\n        // this.availableFiles = response.data.files || []\r\n\r\n        // 模拟加载延迟\r\n        await new Promise(resolve => setTimeout(resolve, 500))\r\n        this.$message.success('文件列表加载完成')\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        this.$message.error('加载文件列表失败')\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理文件...'\r\n\r\n      try {\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 调用后端异常检测接口\r\n        const filenames = this.selectedFiles.map(f => f.fileName)\r\n        console.log('发送到后端的文件名:', filenames)\r\n\r\n        // const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n        //   filenames: filenames\r\n        // })\r\n\r\n        // 模拟处理时间\r\n        await new Promise(resolve => setTimeout(resolve, 3000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        // 模拟生成异常数据\r\n        const mockExceptions = [\r\n          {\r\n            orderNo: 'DD20240715001',\r\n            category: '电子产品',\r\n            specs: '笔记本电脑/16GB 512GB',\r\n            unitPrice: 8999.00,\r\n            quantity: 1,\r\n            totalAmount: 8999.00,\r\n            payerName: '李四',\r\n            idNumber: '310***********5678',\r\n            phone: '13900139000',\r\n            orderDate: '2024-07-15',\r\n            orderTime: '10:15',\r\n            paymentDate: '2024-07-15',\r\n            paymentTime: '10:20',\r\n            logisticsNo: 'WL987654321'\r\n          },\r\n          {\r\n            orderNo: 'DD20240715002',\r\n            category: '服饰',\r\n            specs: '男士T恤/XL码 黑色',\r\n            unitPrice: 89.90,\r\n            quantity: 3,\r\n            totalAmount: 269.70,\r\n            payerName: '王五',\r\n            idNumber: '320***********1234',\r\n            phone: '13800138000',\r\n            orderDate: '2024-07-14',\r\n            orderTime: '14:30',\r\n            paymentDate: '2024-07-14',\r\n            paymentTime: '14:35',\r\n            logisticsNo: 'WL123456789'\r\n          }\r\n        ]\r\n\r\n        this.exceptionList = mockExceptions\r\n        this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n        this.$message.error(`处理失败: ${error.message}`)\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n  height:250px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  height:-10px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}