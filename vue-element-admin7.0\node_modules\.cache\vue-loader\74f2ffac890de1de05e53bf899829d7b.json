{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=style&index=0&id=09ac478a&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749127241460}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1731739006000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOS4iuS8oOWSjOmAieaLqeWuueWZqOagt+W8jyAqLw0KLnVwbG9hZC1hbmQtc2VsZWN0LWNvbnRhaW5lciB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLyog5LiK5Lyg5Yy65Z+f5qC35byPICovDQoudXBsb2FkLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoudXBsb2FkLWRlbW8gew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnVwbG9hZC1kZW1vIC5lbC11cGxvYWQtZHJhZ2dlciB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDE4MHB4Ow0KICBib3JkZXI6IDJweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuM3M7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkLWRyYWdnZXI6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkLWRyYWdnZXIgLmVsLWljb24tdXBsb2FkIHsNCiAgZm9udC1zaXplOiA2N3B4Ow0KICBjb2xvcjogI2MwYzRjYzsNCiAgbWFyZ2luOiA0MHB4IDAgMTZweDsNCiAgbGluZS1oZWlnaHQ6IDUwcHg7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkX190ZXh0IHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoudXBsb2FkLWRlbW8gLmVsLXVwbG9hZF9fdGV4dCBlbSB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXN0eWxlOiBub3JtYWw7DQp9DQoNCi51cGxvYWQtZGVtbyAuZWwtdXBsb2FkX190aXAgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tdG9wOiA3cHg7DQp9DQoNCi51cGxvYWQtYnV0dG9ucyB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTJweDsNCn0NCg0KLnNlbGVjdGlvbi1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgaGVpZ2h0OjI1MHB4Ow0KfQ0KDQouc2VjdGlvbi1oZWFkZXIgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBoZWlnaHQ6LTEwcHg7DQp9DQoNCi5zZWN0aW9uLWhlYWRlciBoMyB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouc2VjdGlvbi1kZXNjIHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQovKiDmlofku7bliJfooajlrrnlmaggKi8NCi5maWxlLWxpc3QtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmZpbGUtdGFibGUtd3JhcHBlciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWF4LWhlaWdodDogNDAwcHg7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0KDQovKiDoh6rlrprkuYnooajmoLzmu5rliqjmnaHmoLflvI8gKi8NCi5maWxlLXRhYmxlLXdyYXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgd2lkdGg6IDhweDsNCiAgaGVpZ2h0OiA4cHg7DQp9DQoNCi5maWxlLXRhYmxlLXdyYXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogI2YxZjFmMTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouZmlsZS10YWJsZS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6ICNjMGM0Y2M7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLmZpbGUtdGFibGUtd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjYThhZWIzOw0KfQ0KDQovKiDlt7LpgInmi6nmlofku7bljLrln58gKi8NCi5zZWxlY3RlZC1maWxlcy1zZWN0aW9uIHsNCiAgbWFyZ2luOiAyMHB4IDA7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQogIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCn0NCg0KLnNlbGVjdGVkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi5zZWxlY3RlZC1maWxlcy1saXN0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBnYXA6IDhweDsNCn0NCg0KLyog5pON5L2c5oyJ6ZKu5Yy65Z+fICovDQouYWN0aW9uLWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEycHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5hY3Rpb24tYnV0dG9ucyAuZWwtYnV0dG9uIHsNCiAgcGFkZGluZzogMTJweCAyMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi8qIOi/m+W6puaYvuekuuWMuuWfnyAqLw0KLnByb2dyZXNzLXNlY3Rpb24gew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoucHJvZ3Jlc3MtdGV4dCB7DQogIG1hcmdpbjogMTBweCAwIDAgMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQovKiDljaHniYfmoLflvI8gKi8NCi5ib3gtY2FyZCB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoNCi5lbC10YWJsZSB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQp9DQoNCi8qIOa7muWKqOWuueWZqCAqLw0KLmN1c3RvbS1zY3JvbGxiYXIgew0KICBoZWlnaHQ6IDEwMCU7DQogIG92ZXJmbG93OiBhdXRvOw0KICBwYWRkaW5nLXJpZ2h0OiAxMnB4Ow0KfQ0KDQovKiDlnoLnm7Tmu5rliqjmnaEgKi8NCi5jdXN0b20tc2Nyb2xsYmFyOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA4cHg7IC8qIOWeguebtOa7muWKqOadoeWuveW6piAqLw0KfQ0KDQovKiDmsLTlubPmu5rliqjmnaEgKi8NCi5jdXN0b20tc2Nyb2xsYmFyOjotd2Via2l0LXNjcm9sbGJhcjpob3Jpem9udGFsIHsNCiAgaGVpZ2h0OiA4cHg7IC8qIOawtOW5s+a7muWKqOadoemrmOW6piAqLw0KICBtYXJnaW4tYm90dG9tOiAwcHg7Ow0KfQ0KDQovKiDmu5rliqjmnaHovajpgZMgKi8NCi5jdXN0b20tc2Nyb2xsYmFyOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6ICNmMWYxZjE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLyog5rua5Yqo5p2h5ruR5Z2XICovDQouY3VzdG9tLXNjcm9sbGJhcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kOiAjYzBjNGNjOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi8qIOa7muWKqOadoea7keWdl+aCrOWBnOaViOaenCAqLw0KLmN1c3RvbS1zY3JvbGxiYXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogI2E4YWViMzsNCn0NCi8qIOa7muWKqOWuueWZqCAqLw0KLyog6KGo5qC85qC35byP5LyY5YyWICovDQouZmlsZS1saXN0LWNvbnRhaW5lciAuZWwtdGFibGUgdGggew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLmZpbGUtbGlzdC1jb250YWluZXIgLmVsLXRhYmxlIHRkIHsNCiAgcGFkZGluZzogMTJweCAwOw0KfQ0KDQouZmlsZS1saXN0LWNvbnRhaW5lciAuZWwtdGFibGUgLmVsLWljb24tZG9jdW1lbnQgew0KICBjb2xvcjogIzY3YzIzYTsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQovKiDooajmoLzooYzmgqzlgZzmlYjmnpwgKi8NCi5maWxlLWxpc3QtY29udGFpbmVyIC5lbC10YWJsZSB0Ym9keSB0cjpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi8qIOiusOW9leaVsOagt+W8jyAqLw0KLmZpbGUtbGlzdC1jb250YWluZXIgLmVsLXRhYmxlIC5yZWNvcmQtY291bnQgew0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzQwOWVmZjsNCn0NCg0KLyog54q25oCB5qCH562+5qC35byP6LCD5pW0ICovDQouZmlsZS1saXN0LWNvbnRhaW5lciAuZWwtdGFnIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCi5zY3JvbGwtY29udGFpbmVyIHsNCiAgaGVpZ2h0OiA2MDBweDsgLyog5Zu65a6a6auY5bqmICovDQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLyog6KGo5qC86auY5bqm6Ieq6YCC5bqU5a655ZmoICovDQouZWwtdGFibGUgew0KICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDsNCn0NCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgfQ0KDQogIC5hY3Rpb24tYnV0dG9ucyAuZWwtYnV0dG9uIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy3BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到服务器</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择文件进行异常检测</h3>\r\n<p class=\"section-desc\">从已上传的文件中选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"200\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\" />\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"uploadDate\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount.toLocaleString() }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedFile(file)\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // 文件选择相关\r\n      availableFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: '订单数据_2024Q1.xlsx',\r\n          uploadDate: '2024-01-15 10:30:00',\r\n          recordCount: 1250,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: '物流信息_2024Q1.xlsx',\r\n          uploadDate: '2024-01-20 14:20:00',\r\n          recordCount: 980,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: '订单数据_2024Q2.xlsx',\r\n          uploadDate: '2024-04-10 09:15:00',\r\n          recordCount: 1680,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: '物流信息_2024Q2.xlsx',\r\n          uploadDate: '2024-04-15 16:45:00',\r\n          recordCount: 1420,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: '订单数据_2024Q3.xlsx',\r\n          uploadDate: '2024-07-08 11:30:00',\r\n          recordCount: 2100,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: '物流信息_2024Q3.xlsx',\r\n          uploadDate: '2024-07-12 15:20:00',\r\n          recordCount: 1890,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 7,\r\n          fileName: '订单数据_2024Q4.xlsx',\r\n          uploadDate: '2024-10-05 09:45:00',\r\n          recordCount: 2350,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 8,\r\n          fileName: '物流信息_2024Q4.xlsx',\r\n          uploadDate: '2024-10-08 14:30:00',\r\n          recordCount: 2180,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 9,\r\n          fileName: '客户信息_2024年度.xlsx',\r\n          uploadDate: '2024-12-01 10:15:00',\r\n          recordCount: 5600,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 10,\r\n          fileName: '供应商数据_2024年度.xlsx',\r\n          uploadDate: '2024-12-02 11:20:00',\r\n          recordCount: 890,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 11,\r\n          fileName: '退货记录_2024Q1-Q2.xlsx',\r\n          uploadDate: '2024-06-30 16:45:00',\r\n          recordCount: 320,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 12,\r\n          fileName: '退货记录_2024Q3-Q4.xlsx',\r\n          uploadDate: '2024-12-15 09:30:00',\r\n          recordCount: 280,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 13,\r\n          fileName: '异常订单_历史数据.xlsx',\r\n          uploadDate: '2024-11-20 13:15:00',\r\n          recordCount: 156,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 14,\r\n          fileName: '物流跟踪_详细记录.xlsx',\r\n          uploadDate: '2024-12-10 08:45:00',\r\n          recordCount: 4200,\r\n          status: 'processing'\r\n        },\r\n        {\r\n          id: 15,\r\n          fileName: '订单统计_月度汇总.xlsx',\r\n          uploadDate: '2024-12-18 14:20:00',\r\n          recordCount: 720,\r\n          status: 'available'\r\n        }\r\n      ],\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [\r\n        {\r\n          orderNo: 'DD20240715001',\r\n          category: '电子产品',\r\n          specs: '笔记本电脑/16GB 512GB',\r\n          unitPrice: 8999.00,\r\n          quantity: 1,\r\n          totalAmount: 8999.00,\r\n          payerName: '李四',\r\n          idNumber: '310***********5678',\r\n          phone: '13900139000',\r\n          orderDate: '2024-07-15',\r\n          orderTime: '10:15',\r\n          paymentDate: '2024-07-15',\r\n          paymentTime: '10:20',\r\n          logisticsNo: 'WL987654321'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        }\r\n      ],\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 这里将来连接后端API上传文件\r\n        // const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n        //   headers: {\r\n        //     'Content-Type': 'multipart/form-data'\r\n        //   },\r\n        //   timeout: 60000\r\n        // })\r\n\r\n        // 模拟上传时间\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 模拟上传成功，添加到可用文件列表\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          const newFile = {\r\n            id: this.availableFiles.length + index + 1,\r\n            fileName: fileItem.name,\r\n            uploadDate: new Date().toLocaleString('zh-CN'),\r\n            recordCount: Math.floor(Math.random() * 5000) + 100, // 模拟记录数\r\n            status: 'available'\r\n          }\r\n          this.availableFiles.push(newFile)\r\n        })\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 这里将来连接后端API获取文件列表\r\n        // const response = await axios.get('http://127.0.0.1:8000/available-files')\r\n        // this.availableFiles = response.data.files || []\r\n\r\n        // 模拟加载延迟\r\n        await new Promise(resolve => setTimeout(resolve, 500))\r\n        this.$message.success('文件列表加载完成')\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        this.$message.error('加载文件列表失败')\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理文件...'\r\n\r\n      try {\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 调用后端异常检测接口\r\n        const filenames = this.selectedFiles.map(f => f.fileName)\r\n        console.log('发送到后端的文件名:', filenames)\r\n\r\n        // const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n        //   filenames: filenames\r\n        // })\r\n\r\n        // 模拟处理时间\r\n        await new Promise(resolve => setTimeout(resolve, 3000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        // 模拟生成异常数据\r\n        const mockExceptions = [\r\n          {\r\n            orderNo: 'DD20240715001',\r\n            category: '电子产品',\r\n            specs: '笔记本电脑/16GB 512GB',\r\n            unitPrice: 8999.00,\r\n            quantity: 1,\r\n            totalAmount: 8999.00,\r\n            payerName: '李四',\r\n            idNumber: '310***********5678',\r\n            phone: '13900139000',\r\n            orderDate: '2024-07-15',\r\n            orderTime: '10:15',\r\n            paymentDate: '2024-07-15',\r\n            paymentTime: '10:20',\r\n            logisticsNo: 'WL987654321'\r\n          },\r\n          {\r\n            orderNo: 'DD20240715002',\r\n            category: '服饰',\r\n            specs: '男士T恤/XL码 黑色',\r\n            unitPrice: 89.90,\r\n            quantity: 3,\r\n            totalAmount: 269.70,\r\n            payerName: '王五',\r\n            idNumber: '320***********1234',\r\n            phone: '13800138000',\r\n            orderDate: '2024-07-14',\r\n            orderTime: '14:30',\r\n            paymentDate: '2024-07-14',\r\n            paymentTime: '14:35',\r\n            logisticsNo: 'WL123456789'\r\n          }\r\n        ]\r\n\r\n        this.exceptionList = mockExceptions\r\n        this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n        this.$message.error(`处理失败: ${error.message}`)\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n  height:250px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  height:-10px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}