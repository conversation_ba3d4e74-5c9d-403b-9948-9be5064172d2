{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1748923676782}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "name", "data", "availableFiles", "id", "fileName", "fileSize", "uploadDate", "recordCount", "status", "selectedFiles", "loadingFiles", "processing", "processProgress", "progressText", "exceptionList", "orderNo", "category", "specs", "unitPrice", "quantity", "totalAmount", "payerName", "idNumber", "phone", "orderDate", "orderTime", "paymentDate", "paymentTime", "logisticsNo", "scrollContainer", "mounted", "loadAvailableFiles", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "Promise", "resolve", "setTimeout", "$message", "success", "t0", "console", "error", "finish", "stop", "handleSelectionChange", "selection", "log", "removeSelectedFile", "file", "_this2", "index", "findIndex", "f", "splice", "$nextTick", "table", "$refs", "fileTable", "toggleRowSelection", "clearSelection", "_this3", "info", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "processSelectedFiles", "_this4", "_callee2", "progressInterval", "mockExceptions", "_callee2$", "_context2", "length", "warning", "abrupt", "setInterval", "random", "currentStep", "steps", "clearInterval", "concat", "message", "handleScroll", "event"], "sources": ["src/components/Charts/OrderException.vue"], "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"file-selection-container\">\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>从数据库选择Excel文件</h3>\r\n<p class=\"section-desc\">选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\n@selection-change=\"handleSelectionChange\"\r\nstyle=\"width: 100%\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\">\r\n</el-table-column>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\"></i>\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"fileSize\" label=\"文件大小\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n{{ formatFileSize(row.fileSize) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"uploadDate\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"100\" align=\"center\" />\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\n@close=\"removeSelectedFile(file)\"\r\nstyle=\"margin: 4px;\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n@click=\"loadAvailableFiles\"\r\n:loading=\"loadingFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '处理中...' : '合并分析数据' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"processProgress\"\r\n:status=\"processProgress === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n>\r\n</el-progress>\r\n<p class=\"progress-text\">{{ progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"category\" label=\"商品品类\" width=\"120\" />\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"orderTime\" label=\"下单时间\" width=\"100\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentTime\" label=\"支付时间\" width=\"100\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件选择相关\r\n      availableFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: '订单数据_2024Q1.xlsx',\r\n          fileSize: 2048576, // 2MB\r\n          uploadDate: '2024-01-15 10:30:00',\r\n          recordCount: 1250,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: '物流信息_2024Q1.xlsx',\r\n          fileSize: 1536000, // 1.5MB\r\n          uploadDate: '2024-01-20 14:20:00',\r\n          recordCount: 980,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: '订单数据_2024Q2.xlsx',\r\n          fileSize: 3072000, // 3MB\r\n          uploadDate: '2024-04-10 09:15:00',\r\n          recordCount: 1680,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: '物流信息_2024Q2.xlsx',\r\n          fileSize: 2560000, // 2.5MB\r\n          uploadDate: '2024-04-15 16:45:00',\r\n          recordCount: 1420,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: '订单数据_2024Q3.xlsx',\r\n          fileSize: 4096000, // 4MB\r\n          uploadDate: '2024-07-08 11:30:00',\r\n          recordCount: 2100,\r\n          status: 'available'\r\n        }\r\n      ],\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [\r\n        {\r\n          orderNo: 'DD20240715001',\r\n          category: '电子产品',\r\n          specs: '笔记本电脑/16GB 512GB',\r\n          unitPrice: 8999.00,\r\n          quantity: 1,\r\n          totalAmount: 8999.00,\r\n          payerName: '李四',\r\n          idNumber: '310***********5678',\r\n          phone: '13900139000',\r\n          orderDate: '2024-07-15',\r\n          orderTime: '10:15',\r\n          paymentDate: '2024-07-15',\r\n          paymentTime: '10:20',\r\n          logisticsNo: 'WL987654321'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        }\r\n      ],\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 这里将来连接后端API获取文件列表\r\n        // const response = await axios.get('http://127.0.0.1:8000/available-files')\r\n        // this.availableFiles = response.data.files || []\r\n\r\n        // 模拟加载延迟\r\n        await new Promise(resolve => setTimeout(resolve, 500))\r\n        this.$message.success('文件列表加载完成')\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        this.$message.error('加载文件列表失败')\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    },\r\n\r\n    // 处理选中的文件\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理文件...'\r\n\r\n      try {\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 这里将来连接后端API处理文件\r\n        // const fileIds = this.selectedFiles.map(f => f.id)\r\n        // const response = await axios.post('http://127.0.0.1:8000/process-files', {\r\n        //   fileIds: fileIds\r\n        // })\r\n\r\n        // 模拟处理时间\r\n        await new Promise(resolve => setTimeout(resolve, 3000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        // 模拟生成异常数据\r\n        const mockExceptions = [\r\n          {\r\n            orderNo: 'DD20240715001',\r\n            category: '电子产品',\r\n            specs: '笔记本电脑/16GB 512GB',\r\n            unitPrice: 8999.00,\r\n            quantity: 1,\r\n            totalAmount: 8999.00,\r\n            payerName: '李四',\r\n            idNumber: '310***********5678',\r\n            phone: '13900139000',\r\n            orderDate: '2024-07-15',\r\n            orderTime: '10:15',\r\n            paymentDate: '2024-07-15',\r\n            paymentTime: '10:20',\r\n            logisticsNo: 'WL987654321'\r\n          },\r\n          {\r\n            orderNo: 'DD20240715002',\r\n            category: '服饰',\r\n            specs: '男士T恤/XL码 黑色',\r\n            unitPrice: 89.90,\r\n            quantity: 3,\r\n            totalAmount: 269.70,\r\n            payerName: '王五',\r\n            idNumber: '320***********1234',\r\n            phone: '13800138000',\r\n            orderDate: '2024-07-14',\r\n            orderTime: '14:30',\r\n            paymentDate: '2024-07-14',\r\n            paymentTime: '14:35',\r\n            logisticsNo: 'WL123456789'\r\n          }\r\n        ]\r\n\r\n        this.exceptionList = mockExceptions\r\n        this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)\r\n\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n        this.$message.error(`处理失败: ${error.message}`)\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 文件选择容器样式 */\r\n.file-selection-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  height: 600px;\r\n  position: relative;\r\n}\r\n\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA,GACA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,QAAA;QACAC,QAAA;QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA,EACA;MACAC,aAAA;MACAC,YAAA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,aAAA,GACA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,GACA;QACAb,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA,EACA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAhB,aAAA;IACA;IACA,KAAAiB,kBAAA;EACA;EACAC,OAAA;IACA;IACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,KAAA,CAAAvB,YAAA;cAAA8B,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAOA,IAAAC,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cACAX,KAAA,CAAAa,QAAA,CAAAC,OAAA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAC,KAAA,cAAAV,QAAA,CAAAQ,EAAA;cACAf,KAAA,CAAAa,QAAA,CAAAI,KAAA;YAAA;cAAAV,QAAA,CAAAC,IAAA;cAEAR,KAAA,CAAAvB,YAAA;cAAA,OAAA8B,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IAEA;IAEA;IACAgB,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,aAAA,GAAA6C,SAAA;MACAL,OAAA,CAAAM,GAAA,WAAAD,SAAA;IACA;IAEA;IACAE,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA,QAAAlD,aAAA,CAAAmD,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA1D,EAAA,KAAAsD,IAAA,CAAAtD,EAAA;MAAA;MACA,IAAAwD,KAAA;QACA,KAAAlD,aAAA,CAAAqD,MAAA,CAAAH,KAAA;MACA;MACA;MACA,KAAAI,SAAA;QACA,IAAAC,KAAA,GAAAN,MAAA,CAAAO,KAAA,CAAAC,SAAA;QACA,IAAAF,KAAA;UACAA,KAAA,CAAAG,kBAAA,CAAAV,IAAA;QACA;MACA;IACA;IAEA;IACAW,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA5D,aAAA;MACA;MACA,KAAAsD,SAAA;QACA,IAAAC,KAAA,GAAAK,MAAA,CAAAJ,KAAA,CAAAC,SAAA;QACA,IAAAF,KAAA;UACAA,KAAA,CAAAI,cAAA;QACA;MACA;MACA,KAAAtB,QAAA,CAAAwB,IAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAC,CAAA;MACA,IAAAC,KAAA;MACA,IAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAArB,GAAA,CAAAiB,KAAA,IAAAI,IAAA,CAAArB,GAAA,CAAAkB,CAAA;MACA,OAAAK,UAAA,EAAAN,KAAA,GAAAI,IAAA,CAAAG,GAAA,CAAAN,CAAA,EAAAE,CAAA,GAAAK,OAAA,aAAAN,KAAA,CAAAC,CAAA;IACA;IAEA;IACAM,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MAAA,OAAAhD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,IAAAC,gBAAA,EAAAC,cAAA;QAAA,OAAAlD,mBAAA,GAAAG,IAAA,UAAAgD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;YAAA;cAAA,MACAwC,MAAA,CAAAzE,aAAA,CAAA+E,MAAA;gBAAAD,SAAA,CAAA7C,IAAA;gBAAA;cAAA;cACAwC,MAAA,CAAApC,QAAA,CAAA2C,OAAA;cAAA,OAAAF,SAAA,CAAAG,MAAA;YAAA;cAIAR,MAAA,CAAAvE,UAAA;cACAuE,MAAA,CAAAtE,eAAA;cACAsE,MAAA,CAAArE,YAAA;cAAA0E,SAAA,CAAA9C,IAAA;cAGA;cACA2C,gBAAA,GAAAO,WAAA;gBACA,IAAAT,MAAA,CAAAtE,eAAA;kBACAsE,MAAA,CAAAtE,eAAA,IAAAgE,IAAA,CAAAgB,MAAA;kBACA,IAAAC,WAAA,GAAAjB,IAAA,CAAAC,KAAA,CAAAK,MAAA,CAAAtE,eAAA;kBACA,IAAAkF,KAAA;kBACAZ,MAAA,CAAArE,YAAA,GAAAiF,KAAA,CAAAD,WAAA;gBACA;cACA,SAEA;cACA;cACA;cACA;cACA;cAEA;cAAAN,SAAA,CAAA7C,IAAA;cAAA,OACA,IAAAC,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAEAmD,aAAA,CAAAX,gBAAA;cACAF,MAAA,CAAAtE,eAAA;cACAsE,MAAA,CAAArE,YAAA;;cAEA;cACAwE,cAAA,IACA;gBACAtE,OAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,WAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,SAAA;gBACAC,SAAA;gBACAC,WAAA;gBACAC,WAAA;gBACAC,WAAA;cACA,GACA;gBACAb,OAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,WAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,SAAA;gBACAC,SAAA;gBACAC,WAAA;gBACAC,WAAA;gBACAC,WAAA;cACA,EACA;cAEAsD,MAAA,CAAApE,aAAA,GAAAuE,cAAA;cACAH,MAAA,CAAApC,QAAA,CAAAC,OAAA,6BAAAiD,MAAA,CAAAd,MAAA,CAAAzE,aAAA,CAAA+E,MAAA,4CAAAQ,MAAA,CAAAd,MAAA,CAAApE,aAAA,CAAA0E,MAAA;cAAAD,SAAA,CAAA7C,IAAA;cAAA;YAAA;cAAA6C,SAAA,CAAA9C,IAAA;cAAA8C,SAAA,CAAAvC,EAAA,GAAAuC,SAAA;cAGAtC,OAAA,CAAAC,KAAA,UAAAqC,SAAA,CAAAvC,EAAA;cACAkC,MAAA,CAAAtE,eAAA;cACAsE,MAAA,CAAArE,YAAA;cACAqE,MAAA,CAAApC,QAAA,CAAAI,KAAA,8BAAA8C,MAAA,CAAAT,SAAA,CAAAvC,EAAA,CAAAiD,OAAA;YAAA;cAAAV,SAAA,CAAA9C,IAAA;cAEAyC,MAAA,CAAAvE,UAAA;cACAkC,UAAA;gBACAqC,MAAA,CAAAtE,eAAA;gBACAsE,MAAA,CAAArE,YAAA;cACA;cAAA,OAAA0E,SAAA,CAAApC,MAAA;YAAA;YAAA;cAAA,OAAAoC,SAAA,CAAAnC,IAAA;UAAA;QAAA,GAAA+B,QAAA;MAAA;IAEA;IAEAe,YAAA,WAAAA,aAAAC,KAAA;MACA;MACAlD,OAAA,CAAAM,GAAA,iBAAA4C,KAAA;IACA;EACA;AACA", "ignoreList": []}]}