{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749133725761}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5paH5Lu25LiK5Lyg55u45YWzDQogICAgICB1cGxvYWRGaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBsb2FkUHJvZ3Jlc3M6IDAsDQogICAgICB1cGxvYWRQcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyBFeGNlbOaWh+S7tumAieaLqeebuOWFsw0KICAgICAgYXZhaWxhYmxlVGFibGVzOiBbXSwgLy8g5LuO5ZCO56uv5Yqo5oCB5Yqg6L29DQogICAgICBzZWxlY3RlZFRhYmxlczogW10sDQogICAgICBsb2FkaW5nRmlsZXM6IGZhbHNlLA0KICAgICAgcHJvY2Vzc2luZzogZmFsc2UsDQogICAgICBwcm9jZXNzUHJvZ3Jlc3M6IDAsDQogICAgICBwcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDlvILluLjmlbDmja7liJfooagNCiAgICAgIGV4Y2VwdGlvbkxpc3Q6IFtdLCAvLyDku47lkI7nq6/lvILluLjmo4DmtYvojrflj5YNCiAgICAgIGV4Y2VwdGlvbkNvbHVtbnM6IFtdLCAvLyDliqjmgIHnlJ/miJDnmoTooajmoLzliJcNCiAgICAgIHNjcm9sbENvbnRhaW5lcjogbnVsbA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJbml7bmuIXnqbrlvILluLjmlbDmja7liJfooajvvIznrYnlvoXnlKjmiLfpgInmi6nmlofku7YNCiAgICB0aGlzLmV4Y2VwdGlvbkxpc3QgPSBbXQ0KICAgIC8vIOWKoOi9veWPr+eUqOaWh+S7tuWIl+ihqA0KICAgIHRoaXMubG9hZEF2YWlsYWJsZUZpbGVzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaWh+S7tuS4iuS8oOebuOWFs+aWueazlQ0KICAgIGhhbmRsZUZpbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkRmlsZUxpc3QgPSBmaWxlTGlzdA0KICAgICAgY29uc29sZS5sb2coJ+S4iuS8oOaWh+S7tuWIl+ihqOabtOaWsDonLCBmaWxlTGlzdCkNCiAgICB9LA0KDQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IGZpbGVMaXN0DQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25bey56e76ZmkOicsIGZpbGUubmFtZSkNCiAgICB9LA0KDQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwNCiAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwDQoNCiAgICAgIGlmICghaXNFeGNlbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDEwTSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4cxME1CIScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlIC8vIOmYu+atouiHquWKqOS4iuS8oO+8jOaJi+WKqOaOp+WItg0KICAgIH0sDQoNCiAgICBjbGVhclVwbG9hZEZpbGVzKCkgew0KICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCkNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF56m65LiK5Lyg5paH5Lu25YiX6KGoJykNCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlVXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMudXBsb2FkRmlsZUxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5LiK5Lyg55qERXhjZWzmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMA0KICAgICAgdGhpcy51cGxvYWRQcm9ncmVzc1RleHQgPSAn5YeG5aSH5LiK5Lyg5paH5Lu2Li4uJw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQoNCiAgICAgICAgLy8g5re75Yqg5omA5pyJ5paH5Lu25YiwRm9ybURhdGENCiAgICAgICAgdGhpcy51cGxvYWRGaWxlTGlzdC5mb3JFYWNoKChmaWxlSXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGVzJywgZmlsZUl0ZW0ucmF3KQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnVwbG9hZFByb2dyZXNzIDwgOTApIHsNCiAgICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgKz0gTWF0aC5yYW5kb20oKSAqIDEwDQogICAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9IGDmraPlnKjkuIrkvKDmlofku7YuLi4gJHtNYXRoLnJvdW5kKHRoaXMudXBsb2FkUHJvZ3Jlc3MpfSVgDQogICAgICAgICAgfQ0KICAgICAgICB9LCAyMDApDQoNCiAgICAgICAgLy8g55yf5q2j6LCD55So5ZCO56uvQVBJ5LiK5Lyg5paH5Lu2DQogICAgICAgIC8vIOazqOaEj++8muWmguaenOWQjuerr+ayoeacieWunueOsCAvdXBsb2FkLWZpbGVzIOaOpeWPo++8jOivt+azqOmHiuaOieS4i+mdoueahOS7o+egge+8jOS9v+eUqOaooeaLn+S4iuS8oA0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL3VwbG9hZC1maWxlcycsIGZvcm1EYXRhLCB7DQogICAgICAgICAgICBoZWFkZXJzOiB7DQogICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB0aW1lb3V0OiA2MDAwMA0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDmo4Dmn6XkuIrkvKDnu5PmnpwNCiAgICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEgfHwgIXJlc3BvbnNlLmRhdGEuc3VjY2Vzcykgew0KICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2UgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoICh1cGxvYWRFcnJvcikgew0KICAgICAgICAgIC8vIOWmguaenOS4iuS8oOaOpeWPo+S4jeWtmOWcqO+8jOS9v+eUqOaooeaLn+S4iuS8oA0KICAgICAgICAgIGlmICh1cGxvYWRFcnJvci5yZXNwb25zZSAmJiB1cGxvYWRFcnJvci5yZXNwb25zZS5zdGF0dXMgPT09IDQwNCkgew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfkuIrkvKDmjqXlj6PkuI3lrZjlnKjvvIzkvb/nlKjmqKHmi5/kuIrkvKAnKQ0KICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aHJvdyB1cGxvYWRFcnJvcg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGNsZWFySW50ZXJ2YWwocHJvZ3Jlc3NJbnRlcnZhbCkNCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDEwMA0KICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICfmlofku7bkuIrkvKDlrozmiJDvvIEnDQoNCiAgICAgICAgLy8g5LiK5Lyg5oiQ5Yqf5ZCO77yM6YeN5paw5Yqg6L295pyN5Yqh5Zmo5LiK55qERXhjZWzmlofku7bliJfooagNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkQXZhaWxhYmxlRmlsZXMoKQ0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5LiK5LygICR7dGhpcy51cGxvYWRGaWxlTGlzdC5sZW5ndGh9IOS4quaWh+S7tmApDQogICAgICAgIHRoaXMuY2xlYXJVcGxvYWRGaWxlcygpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfkuIrkvKDlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwDQogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3NUZXh0ID0gJycNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDANCiAgICAgICAgICB0aGlzLnVwbG9hZFByb2dyZXNzVGV4dCA9ICcnDQogICAgICAgIH0sIDMwMDApDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWPr+eUqOaVsOaNruihqOWIl+ihqA0KICAgIGFzeW5jIGxvYWRBdmFpbGFibGVGaWxlcygpIHsNCiAgICAgIHRoaXMubG9hZGluZ0ZpbGVzID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55So5ZCO56uvQVBJ6I635Y+W5omA5pyJRXhjZWzmlofku7bot6/lvoQNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvZ2V0X2FsbF9UcmFja2luZ051bScpDQogICAgICAgIGNvbnNvbGUubG9nKCflkI7nq6/ov5Tlm57nmoRFeGNlbOaWh+S7tui3r+W+hDonLCByZXNwb25zZS5kYXRhKQ0KICAgICAgICBjb25zb2xlLmxvZygn6L+Z5Lqb5paH5Lu25p2l6IeqcGF0aF9kZWZhdWx05paH5Lu25aS5OicsIHJlc3BvbnNlLmRhdGEucGF0aHMpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5wYXRocykgew0KICAgICAgICAgIC8vIOWwhuaWh+S7tui3r+W+hOi9rOaNouS4uuWJjeerr+aYvuekuuagvOW8jw0KICAgICAgICAgIHRoaXMuYXZhaWxhYmxlVGFibGVzID0gcmVzcG9uc2UuZGF0YS5wYXRocy5tYXAoKGZpbGVQYXRoLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgLy8g5o+Q5Y+W5paH5Lu25ZCN5L2c5Li66KGo5ZCN5pi+56S6DQogICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGZpbGVQYXRoLnNwbGl0KCdcXCcpLnBvcCgpIHx8IGZpbGVQYXRoLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICAgIGNvbnN0IHRhYmxlTmFtZSA9IGZpbGVOYW1lLnJlcGxhY2UoJy54bHN4JywgJycpIC8vIOenu+mZpOaJqeWxleWQjQ0KDQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBpZDogaW5kZXggKyAxLA0KICAgICAgICAgICAgICB0YWJsZU5hbWU6IHRhYmxlTmFtZSwgLy8g5pi+56S65paH5Lu25ZCN77yI5LiN5ZCr5omp5bGV5ZCN77yJDQogICAgICAgICAgICAgIGZpbGVQYXRoOiBmaWxlUGF0aCwgLy8g5L+d5a2Y5a6M5pW06Lev5b6E55So5LqO5ZCO56uv5aSE55CGDQogICAgICAgICAgICAgIGNyZWF0ZURhdGU6ICcyMDI0LTEyLTIwIDEwOjAwOjAwJywgLy8g5ZCO56uv5rKh5pyJ5o+Q5L6b5pe26Ze077yM5L2/55So6buY6K6k5YC8DQogICAgICAgICAgICAgIHJlY29yZENvdW50OiBudWxsLCAvLyDlkI7nq6/msqHmnInmj5DkvpvorrDlvZXmlbANCiAgICAgICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDliqDovb3kuoYgJHt0aGlzLmF2YWlsYWJsZVRhYmxlcy5sZW5ndGh9IOS4qkV4Y2Vs5paH5Lu2YCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieaJvuWIsOWPr+eUqOeahEV4Y2Vs5paH5Lu2JykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L29RXhjZWzmlofku7bliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vUV4Y2Vs5paH5Lu25YiX6KGo5aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZ0ZpbGVzID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CGRXhjZWzmlofku7bpgInmi6nlj5jljJYNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGVkVGFibGVzID0gc2VsZWN0aW9uDQogICAgICBjb25zb2xlLmxvZygn5bey6YCJ5oupRXhjZWzmlofku7Y6Jywgc2VsZWN0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTlt7LpgInmi6nnmoRFeGNlbOaWh+S7tg0KICAgIHJlbW92ZVNlbGVjdGVkVGFibGUodGFibGUpIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5zZWxlY3RlZFRhYmxlcy5maW5kSW5kZXgodCA9PiB0LmlkID09PSB0YWJsZS5pZCkNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRUYWJsZXMuc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgfQ0KICAgICAgLy8g5ZCM5pe25pu05paw6KGo5qC86YCJ5oup54q25oCBDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IHRhYmxlUmVmID0gdGhpcy4kcmVmcy50YWJsZUxpc3QNCiAgICAgICAgaWYgKHRhYmxlUmVmKSB7DQogICAgICAgICAgdGFibGVSZWYudG9nZ2xlUm93U2VsZWN0aW9uKHRhYmxlLCBmYWxzZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5riF56m66YCJ5oupDQogICAgY2xlYXJTZWxlY3Rpb24oKSB7DQogICAgICB0aGlzLnNlbGVjdGVkVGFibGVzID0gW10NCiAgICAgIC8vIOa4heepuuihqOagvOmAieaLqQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZVJlZiA9IHRoaXMuJHJlZnMudGFibGVMaXN0DQogICAgICAgIGlmICh0YWJsZVJlZikgew0KICAgICAgICAgIHRhYmxlUmVmLmNsZWFyU2VsZWN0aW9uKCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF56m6RXhjZWzmlofku7bpgInmi6knKQ0KICAgIH0sDQogICAgYXN5bmMgcHJvY2Vzc1NlbGVjdGVkVGFibGVzKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRUYWJsZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5aSE55CG55qERXhjZWzmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5wcm9jZXNzaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICflvIDlp4vlpITnkIZFeGNlbOaWh+S7ti4uLicNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6L+b5bqm5pu05pawDQogICAgICAgIGNvbnN0IHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMucHJvY2Vzc1Byb2dyZXNzIDwgODApIHsNCiAgICAgICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzICs9IE1hdGgucmFuZG9tKCkgKiAxMA0KICAgICAgICAgICAgY29uc3QgY3VycmVudFN0ZXAgPSBNYXRoLmZsb29yKHRoaXMucHJvY2Vzc1Byb2dyZXNzIC8gMjUpDQogICAgICAgICAgICBjb25zdCBzdGVwcyA9IFsn5q2j5Zyo6K+75Y+WRXhjZWzmlofku7YuLi4nLCAn5q2j5Zyo5ZCI5bm25pWw5o2uLi4uJywgJ+ato+WcqOWIhuaekOW8guW4uC4uLicsICflpITnkIbkuK0uLi4nXQ0KICAgICAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSBzdGVwc1tjdXJyZW50U3RlcF0gfHwgJ+WkhOeQhuS4rS4uLicNCiAgICAgICAgICB9DQogICAgICAgIH0sIDUwMCkNCg0KICAgICAgICAvLyDosIPnlKjlkI7nq6/lvILluLjmo4DmtYvmjqXlj6MNCiAgICAgICAgY29uc3QgZmlsZVBhdGhzID0gdGhpcy5zZWxlY3RlZFRhYmxlcy5tYXAodCA9PiB0LmZpbGVQYXRoKQ0KICAgICAgICBjb25zb2xlLmxvZygn6YCJ5Lit55qE6KGo5qC85pWw5o2uOicsIHRoaXMuc2VsZWN0ZWRUYWJsZXMpDQogICAgICAgIGNvbnNvbGUubG9nKCflj5HpgIHliLDlkI7nq6/nmoTmlofku7bot6/lvoQ6JywgZmlsZVBhdGhzKQ0KICAgICAgICBjb25zb2xlLmxvZygn6L+Z5Lqb6Lev5b6E5p2l6IeqcGF0aF9kZWZhdWx05paH5Lu25aS5OicsIGZpbGVQYXRocykNCg0KICAgICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICfmraPlnKjosIPnlKjlkI7nq6/liIbmnpDmjqXlj6MuLi4nDQoNCiAgICAgICAgLy8g55yf5q2j6LCD55So5ZCO56uvQVBJDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2dldF9zdXNfVHJhY2tpbmdOdW0nLCB7DQogICAgICAgICAgZmlsZW5hbWVzOiBmaWxlUGF0aHMNCiAgICAgICAgfSkNCg0KICAgICAgICBjbGVhckludGVydmFsKHByb2dyZXNzSW50ZXJ2YWwpDQogICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMTAwDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+aVsOaNruWkhOeQhuWujOaIkO+8gScNCg0KICAgICAgICBjb25zb2xlLmxvZygn5ZCO56uv6L+U5Zue55qE5byC5bi45qOA5rWL57uT5p6cOicsIHJlc3BvbnNlLmRhdGEpDQoNCiAgICAgICAgLy8g5aSE55CG5ZCO56uv6L+U5Zue55qE5byC5bi45pWw5o2uDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgY29uc3QgZXhjZXB0aW9uTGlzdCA9IFtdDQogICAgICAgICAgY29uc3QgYWxsQ29sdW1ucyA9IG5ldyBTZXQoKSAvLyDnlKjkuo7mlLbpm4bmiYDmnInlj6/og73nmoTliJflkI0NCg0KICAgICAgICAgIC8vIOmBjeWOhuWQjuerr+i/lOWbnueahOWQhOenjeW8guW4uOexu+Weiw0KICAgICAgICAgIE9iamVjdC5rZXlzKHJlc3BvbnNlLmRhdGEpLmZvckVhY2goZXhjZXB0aW9uVHlwZSA9PiB7DQogICAgICAgICAgICBjb25zdCBleGNlcHRpb25zID0gcmVzcG9uc2UuZGF0YVtleGNlcHRpb25UeXBlXQ0KICAgICAgICAgICAgaWYgKGV4Y2VwdGlvbnMgJiYgZXhjZXB0aW9ucy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIGV4Y2VwdGlvbnMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICAvLyDmlLbpm4bmiYDmnInlrZfmrrXlkI3kvZzkuLrliJflkI0NCiAgICAgICAgICAgICAgICBPYmplY3Qua2V5cyhpdGVtKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgICAgICAgICAgICBhbGxDb2x1bW5zLmFkZChrZXkpDQogICAgICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgICAgIC8vIOebtOaOpeS9v+eUqOWQjuerr+i/lOWbnueahOaVsOaNru+8jOa3u+WKoOW8guW4uOexu+Weiw0KICAgICAgICAgICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IHsNCiAgICAgICAgICAgICAgICAgIC4uLml0ZW0sIC8vIOWxleW8gOWQjuerr+i/lOWbnueahOaJgOacieWtl+autQ0KICAgICAgICAgICAgICAgICAg5byC5bi457G75Z6LOiBleGNlcHRpb25UeXBlIC8vIOa3u+WKoOW8guW4uOexu+Wei+Wtl+autQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBleGNlcHRpb25MaXN0LnB1c2goZXhjZXB0aW9uKQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDnlJ/miJDliqjmgIHooajmoLzliJcNCiAgICAgICAgICB0aGlzLmV4Y2VwdGlvbkNvbHVtbnMgPSBbXQ0KDQogICAgICAgICAgLy8g6aaW5YWI5re75Yqg5byC5bi457G75Z6L5YiXDQogICAgICAgICAgdGhpcy5leGNlcHRpb25Db2x1bW5zLnB1c2goew0KICAgICAgICAgICAgcHJvcDogJ+W8guW4uOexu+WeiycsDQogICAgICAgICAgICBsYWJlbDogJ+W8guW4uOexu+WeiycsDQogICAgICAgICAgICB3aWR0aDogMTUwLA0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLA0KICAgICAgICAgICAgdHlwZTogJ3RhZycNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g54S25ZCO5re75Yqg5YW25LuW5YiXDQogICAgICAgICAgQXJyYXkuZnJvbShhbGxDb2x1bW5zKS5mb3JFYWNoKGNvbHVtbk5hbWUgPT4gew0KICAgICAgICAgICAgdGhpcy5leGNlcHRpb25Db2x1bW5zLnB1c2goew0KICAgICAgICAgICAgICBwcm9wOiBjb2x1bW5OYW1lLA0KICAgICAgICAgICAgICBsYWJlbDogY29sdW1uTmFtZSwNCiAgICAgICAgICAgICAgd2lkdGg6IHRoaXMuZ2V0Q29sdW1uV2lkdGgoY29sdW1uTmFtZSksDQogICAgICAgICAgICAgIGFsaWduOiB0aGlzLmdldENvbHVtbkFsaWduKGNvbHVtbk5hbWUpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICB0aGlzLmV4Y2VwdGlvbkxpc3QgPSBleGNlcHRpb25MaXN0DQoNCiAgICAgICAgICBpZiAoZXhjZXB0aW9uTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WkhOeQhiAke3RoaXMuc2VsZWN0ZWRUYWJsZXMubGVuZ3RofSDkuKpFeGNlbOaWh+S7tu+8jOWPkeeOsCAke2V4Y2VwdGlvbkxpc3QubGVuZ3RofSDmnaHlvILluLjmlbDmja5gKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oYOaIkOWKn+WkhOeQhiAke3RoaXMuc2VsZWN0ZWRUYWJsZXMubGVuZ3RofSDkuKpFeGNlbOaWh+S7tu+8jOacquWPkeeOsOW8guW4uOaVsOaNrmApDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5ZCO56uv6L+U5Zue5pWw5o2u5qC85byP5byC5bi4JykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5aSE55CG5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyA9IDANCiAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAnJw0KDQogICAgICAgIGlmIChlcnJvci5yZXNwb25zZSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOWkhOeQhuWksei0pTogJHtlcnJvci5yZXNwb25zZS5zdGF0dXN9IC0gJHtlcnJvci5yZXNwb25zZS5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2V9YCkNCiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXF1ZXN0KSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc6L+e5o6l5aSx6LSl77yM6K+35qOA5p+l5ZCO56uv5pyN5Yqh5piv5ZCm5ZCv5YqoJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlpITnkIblpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgICB9DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnByb2Nlc3NpbmcgPSBmYWxzZQ0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyA9IDANCiAgICAgICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICcnDQogICAgICAgIH0sIDMwMDApDQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVNjcm9sbChldmVudCkgew0KICAgICAgLy8g5aSE55CG5rua5Yqo5LqL5Lu2DQogICAgICBjb25zb2xlLmxvZygnU2Nyb2xsaW5nLi4uJywgZXZlbnQpDQogICAgfSwNCg0KICAgIC8vIOagueaNruW8guW4uOexu+Wei+i/lOWbnuWvueW6lOeahOagh+etvuminOiJsg0KICAgIGdldEV4Y2VwdGlvblR5cGVDb2xvcihleGNlcHRpb25UeXBlKSB7DQogICAgICBjb25zdCBjb2xvck1hcCA9IHsNCiAgICAgICAgJ+WQjOS4gOWnk+WQjeWkmuS4qui6q+S7veivgSc6ICdkYW5nZXInLA0KICAgICAgICAn5ZCM5LiA6Lqr5Lu96K+B5aSa5Liq5aeT5ZCNJzogJ3dhcm5pbmcnLA0KICAgICAgICAn54mp5rWB5Y2V5Y+36YeN5aSNJzogJ2luZm8nLA0KICAgICAgICAn6K6i5Y2V5Y+35aSa5Liq6Lqr5Lu96K+BJzogJ3N1Y2Nlc3MnDQogICAgICB9DQogICAgICByZXR1cm4gY29sb3JNYXBbZXhjZXB0aW9uVHlwZV0gfHwgJ3ByaW1hcnknDQogICAgfSwNCg0KICAgIC8vIOagueaNruWIl+WQjeiOt+WPluWIl+WuveW6pg0KICAgIGdldENvbHVtbldpZHRoKGNvbHVtbk5hbWUpIHsNCiAgICAgIGNvbnN0IHdpZHRoTWFwID0gew0KICAgICAgICAn6K6i5Y2V5Y+3JzogMTgwLA0KICAgICAgICAn5pSv5LuY5Lq65aeT5ZCNJzogMTIwLA0KICAgICAgICAn5pSv5LuY5Lq66Lqr5Lu96K+B5Y+3JzogMTgwLA0KICAgICAgICAn54mp5rWB5Y2V5Y+3JzogMTgwLA0KICAgICAgICAn5byC5bi457G75Z6LJzogMTUwDQogICAgICB9DQogICAgICByZXR1cm4gd2lkdGhNYXBbY29sdW1uTmFtZV0gfHwgMTIwDQogICAgfSwNCg0KICAgIC8vIOagueaNruWIl+WQjeiOt+WPluWvuem9kOaWueW8jw0KICAgIGdldENvbHVtbkFsaWduKGNvbHVtbk5hbWUpIHsNCiAgICAgIGNvbnN0IGFsaWduTWFwID0gew0KICAgICAgICAn6K6i5Y2V5Y+3JzogJ2NlbnRlcicsDQogICAgICAgICfmlK/ku5jkurrlp5PlkI0nOiAnY2VudGVyJywNCiAgICAgICAgJ+aUr+S7mOS6uui6q+S7veivgeWPtyc6ICdjZW50ZXInLA0KICAgICAgICAn54mp5rWB5Y2V5Y+3JzogJ2NlbnRlcicsDQogICAgICAgICflvILluLjnsbvlnosnOiAnY2VudGVyJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGFsaWduTWFwW2NvbHVtbk5hbWVdIHx8ICdsZWZ0Jw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"400\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\n>\r\n{{ processing ? '处理中...' : '异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column\r\nv-for=\"column in exceptionColumns\"\r\n:key=\"column.prop\"\r\n:prop=\"column.prop\"\r\n:label=\"column.label\"\r\n:width=\"column.width\"\r\n:align=\"column.align\"\r\n>\r\n<template #default=\"{row}\">\r\n<el-tag\r\nv-if=\"column.type === 'tag'\"\r\n:type=\"getExceptionTypeColor(row[column.prop])\"\r\nsize=\"small\"\r\n>\r\n{{ row[column.prop] }}\r\n</el-tag>\r\n<span v-else>{{ row[column.prop] || '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n          const allColumns = new Set() // 用于收集所有可能的列名\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 收集所有字段名作为列名\r\n                Object.keys(item).forEach(key => {\r\n                  allColumns.add(key)\r\n                })\r\n\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  ...item, // 展开后端返回的所有字段\r\n                  异常类型: exceptionType // 添加异常类型字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 生成动态表格列\r\n          this.exceptionColumns = []\r\n\r\n          // 首先添加异常类型列\r\n          this.exceptionColumns.push({\r\n            prop: '异常类型',\r\n            label: '异常类型',\r\n            width: 150,\r\n            align: 'center',\r\n            type: 'tag'\r\n          })\r\n\r\n          // 然后添加其他列\r\n          Array.from(allColumns).forEach(columnName => {\r\n            this.exceptionColumns.push({\r\n              prop: columnName,\r\n              label: columnName,\r\n              width: this.getColumnWidth(columnName),\r\n              align: this.getColumnAlign(columnName)\r\n            })\r\n          })\r\n\r\n          this.exceptionList = exceptionList\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 67px;\r\n  color: #c0c4cc;\r\n  margin: 40px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n.scroll-container {\r\n  height: 600px; /* 固定高度 */\r\n  position: relative;\r\n}\r\n\r\n/* 表格高度自适应容器 */\r\n.el-table {\r\n  height: 100% !important;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}