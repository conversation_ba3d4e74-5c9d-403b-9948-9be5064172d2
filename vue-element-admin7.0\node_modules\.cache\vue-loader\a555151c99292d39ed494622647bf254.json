{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1748923676782}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09yZGVyRXhjZXB0aW9uJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5paH5Lu26YCJ5oup55u45YWzDQogICAgICBhdmFpbGFibGVGaWxlczogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEsDQogICAgICAgICAgZmlsZU5hbWU6ICforqLljZXmlbDmja5fMjAyNFExLnhsc3gnLA0KICAgICAgICAgIGZpbGVTaXplOiAyMDQ4NTc2LCAvLyAyTUINCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wMS0xNSAxMDozMDowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDEyNTAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgZmlsZU5hbWU6ICfnianmtYHkv6Hmga9fMjAyNFExLnhsc3gnLA0KICAgICAgICAgIGZpbGVTaXplOiAxNTM2MDAwLCAvLyAxLjVNQg0KICAgICAgICAgIHVwbG9hZERhdGU6ICcyMDI0LTAxLTIwIDE0OjIwOjAwJywNCiAgICAgICAgICByZWNvcmRDb3VudDogOTgwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIGZpbGVOYW1lOiAn6K6i5Y2V5pWw5o2uXzIwMjRRMi54bHN4JywNCiAgICAgICAgICBmaWxlU2l6ZTogMzA3MjAwMCwgLy8gM01CDQogICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDQtMTAgMDk6MTU6MDAnLA0KICAgICAgICAgIHJlY29yZENvdW50OiAxNjgwLA0KICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIGZpbGVOYW1lOiAn54mp5rWB5L+h5oGvXzIwMjRRMi54bHN4JywNCiAgICAgICAgICBmaWxlU2l6ZTogMjU2MDAwMCwgLy8gMi41TUINCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wNC0xNSAxNjo0NTowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDE0MjAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDUsDQogICAgICAgICAgZmlsZU5hbWU6ICforqLljZXmlbDmja5fMjAyNFEzLnhsc3gnLA0KICAgICAgICAgIGZpbGVTaXplOiA0MDk2MDAwLCAvLyA0TUINCiAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wNy0wOCAxMTozMDowMCcsDQogICAgICAgICAgcmVjb3JkQ291bnQ6IDIxMDAsDQogICAgICAgICAgc3RhdHVzOiAnYXZhaWxhYmxlJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgc2VsZWN0ZWRGaWxlczogW10sDQogICAgICBsb2FkaW5nRmlsZXM6IGZhbHNlLA0KICAgICAgcHJvY2Vzc2luZzogZmFsc2UsDQogICAgICBwcm9jZXNzUHJvZ3Jlc3M6IDAsDQogICAgICBwcm9ncmVzc1RleHQ6ICcnLA0KDQogICAgICAvLyDlvILluLjmlbDmja7liJfooagNCiAgICAgIGV4Y2VwdGlvbkxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAxJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+eUteWtkOS6p+WTgScsDQogICAgICAgICAgc3BlY3M6ICfnrJTorrDmnKznlLXohJEvMTZHQiA1MTJHQicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OTk5LjAwLA0KICAgICAgICAgIHF1YW50aXR5OiAxLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiA4OTk5LjAwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+adjuWbmycsDQogICAgICAgICAgaWROdW1iZXI6ICczMTAqKioqKioqKioqKjU2NzgnLA0KICAgICAgICAgIHBob25lOiAnMTM5MDAxMzkwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTUnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzEwOjE1JywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTUnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTA6MjAnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0w5ODc2NTQzMjEnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBvcmRlck5vOiAnREQyMDI0MDcxNTAwMicsDQogICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgIHNwZWNzOiAn55S35aOrVOaBpC9YTOeggSDpu5HoibInLA0KICAgICAgICAgIHVuaXRQcmljZTogODkuOTAsDQogICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDI2OS43MCwNCiAgICAgICAgICBwYXllck5hbWU6ICfnjovkupQnLA0KICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBvcmRlckRhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgcGF5bWVudERhdGU6ICcyMDI0LTA3LTE0JywNCiAgICAgICAgICBwYXltZW50VGltZTogJzE0OjM1JywNCiAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5pyN6aWwJywNCiAgICAgICAgICBzcGVjczogJ+eUt+Wjq1TmgaQvWEznoIEg6buR6ImyJywNCiAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgIHF1YW50aXR5OiAzLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAyNjkuNzAsDQogICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICBpZE51bWJlcjogJzMyMCoqKioqKioqKioqMTIzNCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgb3JkZXJUaW1lOiAnMTQ6MzAnLA0KICAgICAgICAgIHBheW1lbnREYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgbG9naXN0aWNzTm86ICdXTDEyMzQ1Njc4OScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG9yZGVyTm86ICdERDIwMjQwNzE1MDAyJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+acjemlsCcsDQogICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgdW5pdFByaWNlOiA4OS45MCwNCiAgICAgICAgICBxdWFudGl0eTogMywNCiAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgIHBheWVyTmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgaWROdW1iZXI6ICczMjAqKioqKioqKioqKjEyMzQnLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIG9yZGVyRGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIG9yZGVyVGltZTogJzE0OjMwJywNCiAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgIHBheW1lbnRUaW1lOiAnMTQ6MzUnLA0KICAgICAgICAgIGxvZ2lzdGljc05vOiAnV0wxMjM0NTY3ODknDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBzY3JvbGxDb250YWluZXI6IG51bGwNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy8g5Yid5aeL5YyW5pe25riF56m65byC5bi45pWw5o2u5YiX6KGo77yM562J5b6F55So5oi36YCJ5oup5paH5Lu2DQogICAgdGhpcy5leGNlcHRpb25MaXN0ID0gW10NCiAgICAvLyDliqDovb3lj6/nlKjmlofku7bliJfooagNCiAgICB0aGlzLmxvYWRBdmFpbGFibGVGaWxlcygpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliqDovb3lj6/nlKjmlofku7bliJfooagNCiAgICBhc3luYyBsb2FkQXZhaWxhYmxlRmlsZXMoKSB7DQogICAgICB0aGlzLmxvYWRpbmdGaWxlcyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOi/memHjOWwhuadpei/nuaOpeWQjuerr0FQSeiOt+WPluaWh+S7tuWIl+ihqA0KICAgICAgICAvLyBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2F2YWlsYWJsZS1maWxlcycpDQogICAgICAgIC8vIHRoaXMuYXZhaWxhYmxlRmlsZXMgPSByZXNwb25zZS5kYXRhLmZpbGVzIHx8IFtdDQoNCiAgICAgICAgLy8g5qih5ouf5Yqg6L295bu26L+fDQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuWIl+ihqOWKoOi9veWujOaIkCcpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlofku7bliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaWh+S7tuWIl+ihqOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmdGaWxlcyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaWh+S7tumAieaLqeWPmOWMlg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcyA9IHNlbGVjdGlvbg0KICAgICAgY29uc29sZS5sb2coJ+W3sumAieaLqeaWh+S7tjonLCBzZWxlY3Rpb24pDQogICAgfSwNCg0KICAgIC8vIOenu+mZpOW3sumAieaLqeeahOaWh+S7tg0KICAgIHJlbW92ZVNlbGVjdGVkRmlsZShmaWxlKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuc2VsZWN0ZWRGaWxlcy5maW5kSW5kZXgoZiA9PiBmLmlkID09PSBmaWxlLmlkKQ0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEZpbGVzLnNwbGljZShpbmRleCwgMSkNCiAgICAgIH0NCiAgICAgIC8vIOWQjOaXtuabtOaWsOihqOagvOmAieaLqeeKtuaAgQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMuZmlsZVRhYmxlDQogICAgICAgIGlmICh0YWJsZSkgew0KICAgICAgICAgIHRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihmaWxlLCBmYWxzZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5riF56m66YCJ5oupDQogICAgY2xlYXJTZWxlY3Rpb24oKSB7DQogICAgICB0aGlzLnNlbGVjdGVkRmlsZXMgPSBbXQ0KICAgICAgLy8g5riF56m66KGo5qC86YCJ5oupDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IHRhYmxlID0gdGhpcy4kcmVmcy5maWxlVGFibGUNCiAgICAgICAgaWYgKHRhYmxlKSB7DQogICAgICAgICAgdGFibGUuY2xlYXJTZWxlY3Rpb24oKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXnqbrmlofku7bpgInmi6knKQ0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbmlofku7blpKflsI8NCiAgICBmb3JtYXRGaWxlU2l6ZShieXRlcykgew0KICAgICAgaWYgKGJ5dGVzID09PSAwKSByZXR1cm4gJzAgQicNCiAgICAgIGNvbnN0IGsgPSAxMDI0DQogICAgICBjb25zdCBzaXplcyA9IFsnQicsICdLQicsICdNQicsICdHQiddDQogICAgICBjb25zdCBpID0gTWF0aC5mbG9vcihNYXRoLmxvZyhieXRlcykgLyBNYXRoLmxvZyhrKSkNCiAgICAgIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumAieS4reeahOaWh+S7tg0KICAgIGFzeW5jIHByb2Nlc3NTZWxlY3RlZEZpbGVzKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRGaWxlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHlpITnkIbnmoTmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5wcm9jZXNzaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICflvIDlp4vlpITnkIbmlofku7YuLi4nDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOaooeaLn+i/m+W6puabtOaWsA0KICAgICAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLnByb2Nlc3NQcm9ncmVzcyA8IDkwKSB7DQogICAgICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTUNCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRTdGVwID0gTWF0aC5mbG9vcih0aGlzLnByb2Nlc3NQcm9ncmVzcyAvIDMwKQ0KICAgICAgICAgICAgY29uc3Qgc3RlcHMgPSBbJ+ato+WcqOivu+WPluaWh+S7ti4uLicsICfmraPlnKjlkIjlubbmlbDmja4uLi4nLCAn5q2j5Zyo5YiG5p6Q5byC5bi4Li4uJ10NCiAgICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gc3RlcHNbY3VycmVudFN0ZXBdIHx8ICflpITnkIbkuK0uLi4nDQogICAgICAgICAgfQ0KICAgICAgICB9LCAzMDApDQoNCiAgICAgICAgLy8g6L+Z6YeM5bCG5p2l6L+e5o6l5ZCO56uvQVBJ5aSE55CG5paH5Lu2DQogICAgICAgIC8vIGNvbnN0IGZpbGVJZHMgPSB0aGlzLnNlbGVjdGVkRmlsZXMubWFwKGYgPT4gZi5pZCkNCiAgICAgICAgLy8gY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCdodHRwOi8vMTI3LjAuMC4xOjgwMDAvcHJvY2Vzcy1maWxlcycsIHsNCiAgICAgICAgLy8gICBmaWxlSWRzOiBmaWxlSWRzDQogICAgICAgIC8vIH0pDQoNCiAgICAgICAgLy8g5qih5ouf5aSE55CG5pe26Ze0DQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAzMDAwKSkNCg0KICAgICAgICBjbGVhckludGVydmFsKHByb2dyZXNzSW50ZXJ2YWwpDQogICAgICAgIHRoaXMucHJvY2Vzc1Byb2dyZXNzID0gMTAwDQogICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+aVsOaNruWkhOeQhuWujOaIkO+8gScNCg0KICAgICAgICAvLyDmqKHmi5/nlJ/miJDlvILluLjmlbDmja4NCiAgICAgICAgY29uc3QgbW9ja0V4Y2VwdGlvbnMgPSBbDQogICAgICAgICAgew0KICAgICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDEnLA0KICAgICAgICAgICAgY2F0ZWdvcnk6ICfnlLXlrZDkuqflk4EnLA0KICAgICAgICAgICAgc3BlY3M6ICfnrJTorrDmnKznlLXohJEvMTZHQiA1MTJHQicsDQogICAgICAgICAgICB1bml0UHJpY2U6IDg5OTkuMDAsDQogICAgICAgICAgICBxdWFudGl0eTogMSwNCiAgICAgICAgICAgIHRvdGFsQW1vdW50OiA4OTk5LjAwLA0KICAgICAgICAgICAgcGF5ZXJOYW1lOiAn5p2O5ZubJywNCiAgICAgICAgICAgIGlkTnVtYmVyOiAnMzEwKioqKioqKioqKio1Njc4JywNCiAgICAgICAgICAgIHBob25lOiAnMTM5MDAxMzkwMDAnLA0KICAgICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNScsDQogICAgICAgICAgICBvcmRlclRpbWU6ICcxMDoxNScsDQogICAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTUnLA0KICAgICAgICAgICAgcGF5bWVudFRpbWU6ICcxMDoyMCcsDQogICAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMOTg3NjU0MzIxJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgb3JkZXJObzogJ0REMjAyNDA3MTUwMDInLA0KICAgICAgICAgICAgY2F0ZWdvcnk6ICfmnI3ppbAnLA0KICAgICAgICAgICAgc3BlY3M6ICfnlLflo6tU5oGkL1hM56CBIOm7keiJsicsDQogICAgICAgICAgICB1bml0UHJpY2U6IDg5LjkwLA0KICAgICAgICAgICAgcXVhbnRpdHk6IDMsDQogICAgICAgICAgICB0b3RhbEFtb3VudDogMjY5LjcwLA0KICAgICAgICAgICAgcGF5ZXJOYW1lOiAn546L5LqUJywNCiAgICAgICAgICAgIGlkTnVtYmVyOiAnMzIwKioqKioqKioqKioxMjM0JywNCiAgICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgICAgb3JkZXJEYXRlOiAnMjAyNC0wNy0xNCcsDQogICAgICAgICAgICBvcmRlclRpbWU6ICcxNDozMCcsDQogICAgICAgICAgICBwYXltZW50RGF0ZTogJzIwMjQtMDctMTQnLA0KICAgICAgICAgICAgcGF5bWVudFRpbWU6ICcxNDozNScsDQogICAgICAgICAgICBsb2dpc3RpY3NObzogJ1dMMTIzNDU2Nzg5Jw0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KDQogICAgICAgIHRoaXMuZXhjZXB0aW9uTGlzdCA9IG1vY2tFeGNlcHRpb25zDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5aSE55CGICR7dGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aH0g5Liq5paH5Lu277yM5Y+R546wICR7dGhpcy5leGNlcHRpb25MaXN0Lmxlbmd0aH0g5p2h5byC5bi45pWw5o2uYCkNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5aSE55CG5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnByb2Nlc3NQcm9ncmVzcyA9IDANCiAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlpITnkIblpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5wcm9jZXNzaW5nID0gZmFsc2UNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzUHJvZ3Jlc3MgPSAwDQogICAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAnJw0KICAgICAgICB9LCAzMDAwKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVTY3JvbGwoZXZlbnQpIHsNCiAgICAgIC8vIOWkhOeQhua7muWKqOS6i+S7tg0KICAgICAgY29uc29sZS5sb2coJ1Njcm9sbGluZy4uLicsIGV2ZW50KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"file-selection-container\">\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>从数据库选择Excel文件</h3>\r\n<p class=\"section-desc\">选择一个或多个Excel文件进行合并分析</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\n@selection-change=\"handleSelectionChange\"\r\nstyle=\"width: 100%\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\">\r\n</el-table-column>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\"></i>\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"fileSize\" label=\"文件大小\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n{{ formatFileSize(row.fileSize) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"uploadDate\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"100\" align=\"center\" />\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\n@close=\"removeSelectedFile(file)\"\r\nstyle=\"margin: 4px;\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n@click=\"loadAvailableFiles\"\r\n:loading=\"loadingFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '处理中...' : '合并分析数据' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"processProgress\"\r\n:status=\"processProgress === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n>\r\n</el-progress>\r\n<p class=\"progress-text\">{{ progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常物流订单列表</span>\r\n</div>\r\n<div class=\"scroll-container\">\r\n<div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n<el-table\r\n:data=\"exceptionList\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%; height: 100%\"\r\n>\r\n<el-table-column prop=\"orderNo\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"category\" label=\"商品品类\" width=\"120\" />\r\n<el-table-column prop=\"specs\" label=\"商品规格\" width=\"180\" />\r\n<el-table-column prop=\"unitPrice\" label=\"单价\" align=\"right\" width=\"110\">\r\n<template #default=\"{row}\">\r\n¥{{ row.unitPrice.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"quantity\" label=\"数量\" width=\"80\" align=\"center\" />\r\n<el-table-column prop=\"totalAmount\" label=\"订单金额\" align=\"right\" width=\"130\">\r\n<template #default=\"{row}\">\r\n¥{{ row.totalAmount.toFixed(2) }}\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"payerName\" label=\"支付人\" width=\"120\" />\r\n<el-table-column prop=\"idNumber\" label=\"身份证号\" width=\"180\" />\r\n<el-table-column prop=\"phone\" label=\"联系电话\" width=\"130\" />\r\n<el-table-column prop=\"orderDate\" label=\"下单日期\" width=\"120\" />\r\n<el-table-column prop=\"orderTime\" label=\"下单时间\" width=\"100\" />\r\n<el-table-column prop=\"paymentDate\" label=\"支付日期\" width=\"120\" />\r\n<el-table-column prop=\"paymentTime\" label=\"支付时间\" width=\"100\" />\r\n<el-table-column prop=\"logisticsNo\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-card>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件选择相关\r\n      availableFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: '订单数据_2024Q1.xlsx',\r\n          fileSize: 2048576, // 2MB\r\n          uploadDate: '2024-01-15 10:30:00',\r\n          recordCount: 1250,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: '物流信息_2024Q1.xlsx',\r\n          fileSize: 1536000, // 1.5MB\r\n          uploadDate: '2024-01-20 14:20:00',\r\n          recordCount: 980,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: '订单数据_2024Q2.xlsx',\r\n          fileSize: 3072000, // 3MB\r\n          uploadDate: '2024-04-10 09:15:00',\r\n          recordCount: 1680,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: '物流信息_2024Q2.xlsx',\r\n          fileSize: 2560000, // 2.5MB\r\n          uploadDate: '2024-04-15 16:45:00',\r\n          recordCount: 1420,\r\n          status: 'available'\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: '订单数据_2024Q3.xlsx',\r\n          fileSize: 4096000, // 4MB\r\n          uploadDate: '2024-07-08 11:30:00',\r\n          recordCount: 2100,\r\n          status: 'available'\r\n        }\r\n      ],\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [\r\n        {\r\n          orderNo: 'DD20240715001',\r\n          category: '电子产品',\r\n          specs: '笔记本电脑/16GB 512GB',\r\n          unitPrice: 8999.00,\r\n          quantity: 1,\r\n          totalAmount: 8999.00,\r\n          payerName: '李四',\r\n          idNumber: '310***********5678',\r\n          phone: '13900139000',\r\n          orderDate: '2024-07-15',\r\n          orderTime: '10:15',\r\n          paymentDate: '2024-07-15',\r\n          paymentTime: '10:20',\r\n          logisticsNo: 'WL987654321'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        },\r\n        {\r\n          orderNo: 'DD20240715002',\r\n          category: '服饰',\r\n          specs: '男士T恤/XL码 黑色',\r\n          unitPrice: 89.90,\r\n          quantity: 3,\r\n          totalAmount: 269.70,\r\n          payerName: '王五',\r\n          idNumber: '320***********1234',\r\n          phone: '13800138000',\r\n          orderDate: '2024-07-14',\r\n          orderTime: '14:30',\r\n          paymentDate: '2024-07-14',\r\n          paymentTime: '14:35',\r\n          logisticsNo: 'WL123456789'\r\n        }\r\n      ],\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 这里将来连接后端API获取文件列表\r\n        // const response = await axios.get('http://127.0.0.1:8000/available-files')\r\n        // this.availableFiles = response.data.files || []\r\n\r\n        // 模拟加载延迟\r\n        await new Promise(resolve => setTimeout(resolve, 500))\r\n        this.$message.success('文件列表加载完成')\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        this.$message.error('加载文件列表失败')\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    },\r\n\r\n    // 处理选中的文件\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理文件...'\r\n\r\n      try {\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 这里将来连接后端API处理文件\r\n        // const fileIds = this.selectedFiles.map(f => f.id)\r\n        // const response = await axios.post('http://127.0.0.1:8000/process-files', {\r\n        //   fileIds: fileIds\r\n        // })\r\n\r\n        // 模拟处理时间\r\n        await new Promise(resolve => setTimeout(resolve, 3000))\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        // 模拟生成异常数据\r\n        const mockExceptions = [\r\n          {\r\n            orderNo: 'DD20240715001',\r\n            category: '电子产品',\r\n            specs: '笔记本电脑/16GB 512GB',\r\n            unitPrice: 8999.00,\r\n            quantity: 1,\r\n            totalAmount: 8999.00,\r\n            payerName: '李四',\r\n            idNumber: '310***********5678',\r\n            phone: '13900139000',\r\n            orderDate: '2024-07-15',\r\n            orderTime: '10:15',\r\n            paymentDate: '2024-07-15',\r\n            paymentTime: '10:20',\r\n            logisticsNo: 'WL987654321'\r\n          },\r\n          {\r\n            orderNo: 'DD20240715002',\r\n            category: '服饰',\r\n            specs: '男士T恤/XL码 黑色',\r\n            unitPrice: 89.90,\r\n            quantity: 3,\r\n            totalAmount: 269.70,\r\n            payerName: '王五',\r\n            idNumber: '320***********1234',\r\n            phone: '13800138000',\r\n            orderDate: '2024-07-14',\r\n            orderTime: '14:30',\r\n            paymentDate: '2024-07-14',\r\n            paymentTime: '14:35',\r\n            logisticsNo: 'WL123456789'\r\n          }\r\n        ]\r\n\r\n        this.exceptionList = mockExceptions\r\n        this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)\r\n\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n        this.$message.error(`处理失败: ${error.message}`)\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 文件选择容器样式 */\r\n.file-selection-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  height: 600px;\r\n  position: relative;\r\n}\r\n\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}