{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue?vue&type=style&index=0&id=5fa13db8&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue", "mtime": 1748922947668}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1731739006000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RateGraph.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyRA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RateGraph.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" :disabled=\"!value\" @click=\"confirmSelection\">确认</el-button>\r\n    <el-button type=\"info\" :disabled=\"!value\" @click=\"resetWeights\">重置权重</el-button>\r\n\r\n    <!-- 权重设置区域 -->\r\n    <div class=\"weight-settings\">\r\n      <h3>标签权重设置</h3>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>快进快出：</span>\r\n          <el-slider v-model=\"weights.flag2\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>高频交易：</span>\r\n          <el-slider v-model=\"weights.flag3\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>时间集中：</span>\r\n          <el-slider v-model=\"weights.flag4\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>小额测试：</span>\r\n          <el-slider v-model=\"weights.flag5\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card style=\"margin: 20px; min-height: 520px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>嫌疑排行榜</span>\r\n      </div>\r\n\r\n      <div class=\"scroll-container\">\r\n        <div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n          <el-table :data=\"rankData\" style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"排名\" width=\"80\" />\r\n            <el-table-column prop=\"name\" label=\"姓名 / 公司名称\">\r\n              <template #default=\"scope\">\r\n                <el-link\r\n                  type=\"primary\"\r\n                  @click=\"handleNameClick(scope.row.name)\"\r\n                >\r\n                  {{ scope.row.name }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fused_score\" label=\"嫌疑分值\">\r\n              <template #default=\"scope\">\r\n                {{ Number(scope.row.fused_score).toFixed(3) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"count\" label=\"命中标签数\">\r\n              <template #default=\"scope\">{{ scope.row.count }}</template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"flag2\" label=\"快进快出\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag2 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag3\" label=\"高频交易\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag3 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag4\" label=\"时间集中\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag4 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag5\" label=\"小额测试\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag5 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <div\r\n          v-if=\"isBottom\"\r\n          class=\"bottom-notice\"\r\n          :class=\"{ 'show-notice': isBottom }\"\r\n        >\r\n          已经到达末尾\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  data() {\r\n    return {\r\n      options: [],\r\n      dateRange: [],\r\n      username: null,\r\n      isBottom: false,\r\n      weights: {\r\n        flag2: 1,\r\n        flag3: 1,\r\n        flag4: 1,\r\n        flag5: 1\r\n      },\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      rankData: state => state.rateGraph.rankData,\r\n      weights: state => state.rateGraph.weights,\r\n      dataLoaded: state => state.rateGraph.dataLoaded,\r\n      selectedTable: state => state.rateGraph.selectedTable\r\n    }),\r\n    value: {\r\n      get() {\r\n        return this.selectedTable\r\n      },\r\n      set(value) {\r\n        this.selectTable(value)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch()\r\n    this.scrollContainer = this.$refs.scrollContainer\r\n\r\n    // If we already have a selected table, update the dropdown\r\n    if (this.selectedTable) {\r\n      this.value = this.selectedTable\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateRankData: 'rateGraph/updateRankData',\r\n      selectTable: 'rateGraph/selectTable',\r\n      updateWeights: 'rateGraph/updateWeights',\r\n      resetWeightsAction: 'rateGraph/resetWeights'\r\n    }),\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      if (value) {\r\n        this.$message.info('已选择数据表，请点击「确认」按钮生成排行榜')\r\n      }\r\n    },\r\n    handleNameClick(name) {\r\n      this.$router.push({\r\n        path: '/charts/transmit-detail',\r\n        query: {\r\n          name: encodeURIComponent(name),\r\n          timeUnit: 'month'\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item,\r\n            value: item\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    async generateRankData(weights) {\r\n      try {\r\n        console.log(11)\r\n        if (weights) {\r\n          console.log('发送权重到后端:', weights)\r\n          try {\r\n            const res = await axios.post('http://127.0.0.1:8000/tianye_demo',\r\n              { weights: [this.weights.flag2, this.weights.flag3, this.weights.flag4, this.weights.flag5] }, // 数据\r\n              { // 配置\r\n                headers: {\r\n                  'Content-Type': 'application/json'\r\n                }\r\n              })\r\n            console.log('后端响应:', res)\r\n            console.log('后端', res.data.data)\r\n            this.updateRankData(res.data.data)\r\n          } catch (postError) {\r\n            const getRes = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n            this.updateRankData(getRes.data)\r\n          }\r\n        }\r\n        // } else {\r\n        //   console.log('默认')\r\n        //   try {\r\n        //     const res = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n        //     console.log('获取默认数据成功:', res)\r\n        //     this.updateRankData(res.data.data)\r\n        //   } catch (error) {\r\n        //     console.error('请求失败详细信息:', error)\r\n        //     if (error.response) {\r\n        //       console.error('服务器响应:', error.response.status, error.response.data)\r\n        //     } else if (error.request) {\r\n        //       console.error('没有收到响应，请求详情:', error.request)\r\n        //     } else {\r\n        //       console.error('请求设置错误:', error.message)\r\n        //     }\r\n        //     this.$message.error('连接后端失败，请检查网络和服务器状态')\r\n        //     throw error\r\n        //   }\r\n        // }\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('Error fetching rank data:', error)\r\n        this.$message.error('获取数据失败:', error.message)\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n    confirmSelection() {\r\n      // Only fetch data if we haven't loaded it yet or if the user explicitly wants to refresh\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: '正在处理数据...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(255, 255, 255, 0.7)'\r\n      })\r\n\r\n      // Update the weights in the store\r\n      this.updateWeights(this.weights)\r\n\r\n      this.generateRankData(this.weights)\r\n        .then(() => {\r\n          loading.close()\r\n          this.$message.success('数据处理成功')\r\n        })\r\n        .catch(() => {\r\n          loading.close()\r\n        })\r\n    },\r\n    resetWeights() {\r\n      this.resetWeightsAction()\r\n      this.$message.info('已重置权重为默认值，点击「确认」按钮生效')\r\n    },\r\n    handleScroll({ scrollTop, scrollHeight, clientHeight }) {\r\n      this.isBottom = scrollTop + clientHeight >= scrollHeight - 5\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* Element UI 表格样式覆盖 */\r\n.el-table {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.scroll-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 200px); /* 根据实际情况调整 */\r\n  min-height: 380px;\r\n}\r\n\r\n/* 调整滚动条样式 */\r\n.custom-scrollbar {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px; /* 增加与底部提示的间距 */\r\n}\r\n\r\n/* 底部提示样式优化 */\r\n.bottom-notice {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  color: #909399;\r\n  background: #f5f7fa;\r\n  border-top: 1px solid #dfe6ec;\r\n  transition: all 0.3s;\r\n  opacity: 0;\r\n}\r\n\r\n.show-notice {\r\n  opacity: 1;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n/* 表格列样式微调 */\r\n.el-table-column {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 权重设置区域样式 */\r\n.weight-settings {\r\n  margin: 12px;\r\n  padding: 12px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.weight-settings h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.weight-item {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;  /* 关键：平均分配宽度 */\r\n  height: 32px;\r\n}\r\n\r\n/* 新增：确保滑块容器占满剩余空间 */\r\n.weight-item .el-slider {\r\n  flex: 1;\r\n  min-width: 120px;  /* 防止内容过窄 */\r\n}\r\n\r\n.weight-item span {\r\n  width: 70px;\r\n  text-align: right;\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 保持滑块细节样式 */\r\n.weight-item .el-slider__runway {\r\n  height: 4px !important;\r\n}\r\n.weight-item .el-slider__button {\r\n  width: 12px !important;\r\n  height: 12px !important;\r\n}\r\n</style>\r\n"]}]}